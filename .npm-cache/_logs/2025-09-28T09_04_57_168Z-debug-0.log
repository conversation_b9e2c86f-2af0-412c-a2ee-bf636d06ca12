0 verbose cli /Users/<USER>/.nvm/versions/node/v22.19.0/bin/node /Users/<USER>/.nvm/versions/node/v22.19.0/lib/node_modules/npm/bin/npm-cli.js
1 info using npm@11.6.0
2 info using node@v22.19.0
3 silly config load:file:/Users/<USER>/.nvm/versions/node/v22.19.0/lib/node_modules/npm/npmrc
4 silly config load:file:/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npmrc
5 silly config load:file:/Users/<USER>/.npmrc
6 silly config load:file:/Users/<USER>/.nvm/versions/node/v22.19.0/etc/npmrc
7 verbose title npm exec jest __tests__/lib/valueComparison.test.ts __tests__/lib/variableSelection.test.ts
8 verbose argv "exec" "--" "jest" "__tests__/lib/valueComparison.test.ts" "__tests__/lib/variableSelection.test.ts"
9 verbose logfile logs-max:10 dir:/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_logs/2025-09-28T09_04_57_168Z-
10 verbose logfile /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_logs/2025-09-28T09_04_57_168Z-debug-0.log
11 silly logfile done cleaning log files
12 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
13 verbose shrinkwrap failed to load node_modules/.package-lock.json out of date, updated: node_modules
14 http fetch GET 200 https://registry.npmjs.org/npm 156ms
15 http fetch GET 200 https://registry.npmjs.org/jest 123ms (cache miss)
16 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
17 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
18 warn exec The following package was not found and will be installed: jest@30.2.0
19 silly idealTree buildDeps
20 silly fetch manifest jest@30.2.0
21 silly packumentCache full:https://registry.npmjs.org/jest cache-miss
22 http fetch GET 200 https://registry.npmjs.org/jest 51ms (cache miss)
23 silly packumentCache full:https://registry.npmjs.org/jest set size:undefined disposed:false
24 silly fetch manifest node-notifier@^8.0.1 || ^9.0.0 || ^10.0.0
25 silly packumentCache full:https://registry.npmjs.org/node-notifier cache-miss
26 http fetch GET 200 https://registry.npmjs.org/node-notifier 52ms (cache miss)
27 silly packumentCache full:https://registry.npmjs.org/node-notifier set size:undefined disposed:false
28 silly placeDep ROOT jest@30.2.0 OK for:  want: 30.2.0
29 silly fetch manifest @jest/core@30.2.0
30 silly packumentCache full:https://registry.npmjs.org/@jest%2fcore cache-miss
31 silly fetch manifest @jest/types@30.2.0
32 silly packumentCache full:https://registry.npmjs.org/@jest%2ftypes cache-miss
33 silly fetch manifest import-local@^3.2.0
34 silly packumentCache full:https://registry.npmjs.org/import-local cache-miss
35 silly fetch manifest jest-cli@30.2.0
36 silly packumentCache full:https://registry.npmjs.org/jest-cli cache-miss
37 http fetch GET 200 https://registry.npmjs.org/@jest%2fcore 71ms (cache miss)
38 silly packumentCache full:https://registry.npmjs.org/@jest%2fcore set size:undefined disposed:false
39 http fetch GET 200 https://registry.npmjs.org/import-local 76ms (cache miss)
40 silly packumentCache full:https://registry.npmjs.org/import-local set size:undefined disposed:false
41 http fetch GET 200 https://registry.npmjs.org/@jest%2ftypes 95ms (cache miss)
42 silly packumentCache full:https://registry.npmjs.org/@jest%2ftypes set size:undefined disposed:false
43 http fetch GET 200 https://registry.npmjs.org/jest-cli 104ms (cache miss)
44 silly packumentCache full:https://registry.npmjs.org/jest-cli set size:undefined disposed:false
45 silly placeDep ROOT @jest/core@30.2.0 OK for: jest@30.2.0 want: 30.2.0
46 silly placeDep ROOT @jest/types@30.2.0 OK for: jest@30.2.0 want: 30.2.0
47 silly placeDep ROOT import-local@3.2.0 OK for: jest@30.2.0 want: ^3.2.0
48 silly placeDep ROOT jest-cli@30.2.0 OK for: jest@30.2.0 want: 30.2.0
49 silly fetch manifest @jest/console@30.2.0
50 silly packumentCache full:https://registry.npmjs.org/@jest%2fconsole cache-miss
51 silly fetch manifest @jest/pattern@30.0.1
52 silly packumentCache full:https://registry.npmjs.org/@jest%2fpattern cache-miss
53 silly fetch manifest @jest/reporters@30.2.0
54 silly packumentCache full:https://registry.npmjs.org/@jest%2freporters cache-miss
55 silly fetch manifest @jest/test-result@30.2.0
56 silly packumentCache full:https://registry.npmjs.org/@jest%2ftest-result cache-miss
57 silly fetch manifest @jest/transform@30.2.0
58 silly packumentCache full:https://registry.npmjs.org/@jest%2ftransform cache-miss
59 silly fetch manifest @types/node@*
60 silly packumentCache full:https://registry.npmjs.org/@types%2fnode cache-miss
61 silly fetch manifest ansi-escapes@^4.3.2
62 silly packumentCache full:https://registry.npmjs.org/ansi-escapes cache-miss
63 http fetch GET 200 https://registry.npmjs.org/@jest%2fpattern 52ms (cache miss)
64 silly packumentCache full:https://registry.npmjs.org/@jest%2fpattern set size:undefined disposed:false
65 silly fetch manifest chalk@^4.1.2
66 silly packumentCache full:https://registry.npmjs.org/chalk cache-miss
67 http fetch GET 200 https://registry.npmjs.org/@jest%2fconsole 59ms (cache miss)
68 silly packumentCache full:https://registry.npmjs.org/@jest%2fconsole set size:undefined disposed:false
69 silly fetch manifest ci-info@^4.2.0
70 silly packumentCache full:https://registry.npmjs.org/ci-info cache-miss
71 http fetch GET 200 https://registry.npmjs.org/@jest%2ftest-result 61ms (cache miss)
72 silly packumentCache full:https://registry.npmjs.org/@jest%2ftest-result set size:undefined disposed:false
73 silly fetch manifest exit-x@^0.2.2
74 silly packumentCache full:https://registry.npmjs.org/exit-x cache-miss
75 http fetch GET 200 https://registry.npmjs.org/@jest%2freporters 78ms (cache miss)
76 silly packumentCache full:https://registry.npmjs.org/@jest%2freporters set size:undefined disposed:false
77 silly fetch manifest graceful-fs@^4.2.11
78 silly packumentCache full:https://registry.npmjs.org/graceful-fs cache-miss
79 http fetch GET 200 https://registry.npmjs.org/ansi-escapes 84ms (cache miss)
80 silly packumentCache full:https://registry.npmjs.org/ansi-escapes set size:undefined disposed:false
81 silly fetch manifest jest-changed-files@30.2.0
82 silly packumentCache full:https://registry.npmjs.org/jest-changed-files cache-miss
83 http fetch GET 200 https://registry.npmjs.org/chalk 34ms (cache miss)
84 silly packumentCache full:https://registry.npmjs.org/chalk set size:undefined disposed:false
85 silly fetch manifest jest-config@30.2.0
86 silly packumentCache full:https://registry.npmjs.org/jest-config cache-miss
87 http fetch GET 200 https://registry.npmjs.org/ci-info 34ms (cache miss)
88 silly packumentCache full:https://registry.npmjs.org/ci-info set size:undefined disposed:false
89 silly fetch manifest jest-haste-map@30.2.0
90 silly packumentCache full:https://registry.npmjs.org/jest-haste-map cache-miss
91 http fetch GET 200 https://registry.npmjs.org/exit-x 34ms (cache miss)
92 silly packumentCache full:https://registry.npmjs.org/exit-x set size:undefined disposed:false
93 silly fetch manifest jest-message-util@30.2.0
94 silly packumentCache full:https://registry.npmjs.org/jest-message-util cache-miss
95 http fetch GET 200 https://registry.npmjs.org/graceful-fs 39ms (cache miss)
96 silly packumentCache full:https://registry.npmjs.org/graceful-fs set size:undefined disposed:false
97 silly fetch manifest jest-regex-util@30.0.1
98 silly packumentCache full:https://registry.npmjs.org/jest-regex-util cache-miss
99 http fetch GET 200 https://registry.npmjs.org/jest-changed-files 65ms (cache miss)
100 silly packumentCache full:https://registry.npmjs.org/jest-changed-files set size:undefined disposed:false
101 silly fetch manifest jest-resolve@30.2.0
102 silly packumentCache full:https://registry.npmjs.org/jest-resolve cache-miss
103 http fetch GET 200 https://registry.npmjs.org/jest-config 67ms (cache miss)
104 silly packumentCache full:https://registry.npmjs.org/jest-config set size:undefined disposed:false
105 silly fetch manifest jest-resolve-dependencies@30.2.0
106 silly packumentCache full:https://registry.npmjs.org/jest-resolve-dependencies cache-miss
107 http fetch GET 200 https://registry.npmjs.org/jest-message-util 65ms (cache miss)
108 silly packumentCache full:https://registry.npmjs.org/jest-message-util set size:undefined disposed:false
109 silly fetch manifest jest-runner@30.2.0
110 silly packumentCache full:https://registry.npmjs.org/jest-runner cache-miss
111 http fetch GET 200 https://registry.npmjs.org/jest-haste-map 77ms (cache miss)
112 silly packumentCache full:https://registry.npmjs.org/jest-haste-map set size:undefined disposed:false
113 silly fetch manifest jest-runtime@30.2.0
114 silly packumentCache full:https://registry.npmjs.org/jest-runtime cache-miss
115 http fetch GET 200 https://registry.npmjs.org/jest-regex-util 56ms (cache miss)
116 silly packumentCache full:https://registry.npmjs.org/jest-regex-util set size:undefined disposed:false
117 silly fetch manifest jest-snapshot@30.2.0
118 silly packumentCache full:https://registry.npmjs.org/jest-snapshot cache-miss
119 http fetch GET 200 https://registry.npmjs.org/@jest%2ftransform 182ms (cache miss)
120 silly packumentCache full:https://registry.npmjs.org/@jest%2ftransform set size:undefined disposed:false
121 silly fetch manifest jest-util@30.2.0
122 silly packumentCache full:https://registry.npmjs.org/jest-util cache-miss
123 http fetch GET 200 https://registry.npmjs.org/jest-resolve 47ms (cache miss)
124 silly packumentCache full:https://registry.npmjs.org/jest-resolve set size:undefined disposed:false
125 silly fetch manifest jest-validate@30.2.0
126 silly packumentCache full:https://registry.npmjs.org/jest-validate cache-miss
127 http fetch GET 200 https://registry.npmjs.org/jest-resolve-dependencies 65ms (cache miss)
128 silly packumentCache full:https://registry.npmjs.org/jest-resolve-dependencies set size:undefined disposed:false
129 silly fetch manifest jest-watcher@30.2.0
130 silly packumentCache full:https://registry.npmjs.org/jest-watcher cache-miss
131 http fetch GET 200 https://registry.npmjs.org/jest-runtime 53ms (cache miss)
132 silly packumentCache full:https://registry.npmjs.org/jest-runtime set size:undefined disposed:false
133 silly fetch manifest micromatch@^4.0.8
134 silly packumentCache full:https://registry.npmjs.org/micromatch cache-miss
135 http fetch GET 200 https://registry.npmjs.org/jest-snapshot 75ms (cache miss)
136 silly packumentCache full:https://registry.npmjs.org/jest-snapshot set size:undefined disposed:false
137 silly fetch manifest pretty-format@30.2.0
138 silly packumentCache full:https://registry.npmjs.org/pretty-format cache-miss
139 http fetch GET 200 https://registry.npmjs.org/jest-validate 53ms (cache miss)
140 silly packumentCache full:https://registry.npmjs.org/jest-validate set size:undefined disposed:false
141 silly fetch manifest slash@^3.0.0
142 silly packumentCache full:https://registry.npmjs.org/slash cache-miss
143 http fetch GET 200 https://registry.npmjs.org/micromatch 38ms (cache miss)
144 silly packumentCache full:https://registry.npmjs.org/micromatch set size:undefined disposed:false
145 silly fetch manifest @jest/schemas@30.0.5
146 silly packumentCache full:https://registry.npmjs.org/@jest%2fschemas cache-miss
147 http fetch GET 200 https://registry.npmjs.org/jest-watcher 46ms (cache miss)
148 silly packumentCache full:https://registry.npmjs.org/jest-watcher set size:undefined disposed:false
149 silly fetch manifest @types/istanbul-lib-coverage@^2.0.6
150 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-coverage cache-miss
151 http fetch GET 200 https://registry.npmjs.org/@types%2fnode 274ms (cache miss)
152 silly packumentCache full:https://registry.npmjs.org/@types%2fnode set size:undefined disposed:false
153 silly fetch manifest @types/istanbul-reports@^3.0.4
154 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-reports cache-miss
155 http fetch GET 200 https://registry.npmjs.org/@types%2fistanbul-lib-coverage 50ms (cache miss)
156 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-coverage set size:undefined disposed:false
157 silly fetch manifest @types/yargs@^17.0.33
158 silly packumentCache full:https://registry.npmjs.org/@types%2fyargs cache-miss
159 http fetch GET 200 https://registry.npmjs.org/slash 65ms (cache miss)
160 silly packumentCache full:https://registry.npmjs.org/slash set size:undefined disposed:false
161 silly fetch manifest pkg-dir@^4.2.0
162 silly packumentCache full:https://registry.npmjs.org/pkg-dir cache-miss
163 http fetch GET 200 https://registry.npmjs.org/@jest%2fschemas 57ms (cache miss)
164 silly packumentCache full:https://registry.npmjs.org/@jest%2fschemas set size:undefined disposed:false
165 silly fetch manifest resolve-cwd@^3.0.0
166 silly packumentCache full:https://registry.npmjs.org/resolve-cwd cache-miss
167 http fetch GET 200 https://registry.npmjs.org/jest-util 144ms (cache miss)
168 silly packumentCache full:https://registry.npmjs.org/jest-util set size:undefined disposed:false
169 silly fetch manifest yargs@^17.7.2
170 silly packumentCache full:https://registry.npmjs.org/yargs cache-miss
171 http fetch GET 200 https://registry.npmjs.org/jest-runner 168ms (cache miss)
172 silly packumentCache full:https://registry.npmjs.org/jest-runner set size:undefined disposed:false
173 http fetch GET 200 https://registry.npmjs.org/pretty-format 80ms (cache miss)
174 silly packumentCache full:https://registry.npmjs.org/pretty-format set size:undefined disposed:false
175 http fetch GET 200 https://registry.npmjs.org/resolve-cwd 37ms (cache miss)
176 silly packumentCache full:https://registry.npmjs.org/resolve-cwd set size:undefined disposed:false
177 http fetch GET 200 https://registry.npmjs.org/pkg-dir 38ms (cache miss)
178 silly packumentCache full:https://registry.npmjs.org/pkg-dir set size:undefined disposed:false
179 http fetch GET 200 https://registry.npmjs.org/@types%2fistanbul-reports 47ms (cache miss)
180 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-reports set size:undefined disposed:false
181 http fetch GET 200 https://registry.npmjs.org/@types%2fyargs 69ms (cache miss)
182 silly packumentCache full:https://registry.npmjs.org/@types%2fyargs set size:undefined disposed:false
183 http fetch GET 200 https://registry.npmjs.org/yargs 599ms (cache miss)
184 silly packumentCache full:https://registry.npmjs.org/yargs set size:undefined disposed:false
185 silly fetch manifest esbuild-register@>=3.4.0
186 silly packumentCache full:https://registry.npmjs.org/esbuild-register cache-miss
187 http fetch GET 200 https://registry.npmjs.org/esbuild-register 49ms (cache miss)
188 silly packumentCache full:https://registry.npmjs.org/esbuild-register set size:undefined disposed:false
189 silly fetch manifest esbuild@>=0.12 <1
190 silly packumentCache full:https://registry.npmjs.org/esbuild cache-miss
191 http fetch GET 200 https://registry.npmjs.org/esbuild 55ms (cache miss)
192 silly packumentCache full:https://registry.npmjs.org/esbuild set size:undefined disposed:false
193 silly fetch manifest ts-node@>=9.0.0
194 silly packumentCache full:https://registry.npmjs.org/ts-node cache-miss
195 http fetch GET 200 https://registry.npmjs.org/ts-node 53ms (cache miss)
196 silly packumentCache full:https://registry.npmjs.org/ts-node set size:undefined disposed:false
197 silly fetch manifest @swc/core@>=1.2.50
198 silly packumentCache full:https://registry.npmjs.org/@swc%2fcore cache-miss
199 http fetch GET 200 https://registry.npmjs.org/@swc%2fcore 103ms (cache miss)
200 silly packumentCache full:https://registry.npmjs.org/@swc%2fcore set size:undefined disposed:false
201 silly fetch manifest @swc/helpers@>=0.5.17
202 silly packumentCache full:https://registry.npmjs.org/@swc%2fhelpers cache-miss
203 http fetch GET 200 https://registry.npmjs.org/@swc%2fhelpers 150ms (cache miss)
204 silly packumentCache full:https://registry.npmjs.org/@swc%2fhelpers set size:undefined disposed:false
205 silly fetch manifest @swc/wasm@>=1.2.50
206 silly packumentCache full:https://registry.npmjs.org/@swc%2fwasm cache-miss
207 http fetch GET 200 https://registry.npmjs.org/@swc%2fwasm 105ms (cache miss)
208 silly packumentCache full:https://registry.npmjs.org/@swc%2fwasm set size:undefined disposed:false
209 silly fetch manifest typescript@>=2.7
210 silly packumentCache full:https://registry.npmjs.org/typescript cache-miss
211 http fetch GET 200 https://registry.npmjs.org/typescript 183ms (cache miss)
212 silly packumentCache full:https://registry.npmjs.org/typescript set size:undefined disposed:false
213 silly placeDep ROOT @jest/console@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
214 silly placeDep ROOT @jest/pattern@30.0.1 OK for: @jest/core@30.2.0 want: 30.0.1
215 silly placeDep ROOT @jest/reporters@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
216 silly placeDep ROOT @jest/test-result@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
217 silly placeDep ROOT @jest/transform@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
218 silly placeDep ROOT @types/node@24.5.2 OK for: @jest/core@30.2.0 want: *
219 silly placeDep ROOT ansi-escapes@4.3.2 OK for: @jest/core@30.2.0 want: ^4.3.2
220 silly placeDep ROOT chalk@4.1.2 OK for: @jest/core@30.2.0 want: ^4.1.2
221 silly placeDep ROOT ci-info@4.3.0 OK for: @jest/core@30.2.0 want: ^4.2.0
222 silly placeDep ROOT exit-x@0.2.2 OK for: @jest/core@30.2.0 want: ^0.2.2
223 silly placeDep ROOT graceful-fs@4.2.11 OK for: @jest/core@30.2.0 want: ^4.2.11
224 silly placeDep ROOT jest-changed-files@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
225 silly placeDep ROOT jest-config@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
226 silly placeDep ROOT jest-haste-map@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
227 silly placeDep ROOT jest-message-util@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
228 silly placeDep ROOT jest-regex-util@30.0.1 OK for: @jest/core@30.2.0 want: 30.0.1
229 silly placeDep ROOT jest-resolve@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
230 silly placeDep ROOT jest-resolve-dependencies@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
231 silly placeDep ROOT jest-runner@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
232 silly placeDep ROOT jest-runtime@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
233 silly placeDep ROOT jest-snapshot@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
234 silly placeDep ROOT jest-util@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
235 silly placeDep ROOT jest-validate@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
236 silly placeDep ROOT jest-watcher@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
237 silly placeDep ROOT micromatch@4.0.8 OK for: @jest/core@30.2.0 want: ^4.0.8
238 silly placeDep ROOT pretty-format@30.2.0 OK for: @jest/core@30.2.0 want: 30.2.0
239 silly placeDep ROOT slash@3.0.0 OK for: @jest/core@30.2.0 want: ^3.0.0
240 silly fetch manifest @bcoe/v8-coverage@^0.2.3
241 silly packumentCache full:https://registry.npmjs.org/@bcoe%2fv8-coverage cache-miss
242 silly fetch manifest @jridgewell/trace-mapping@^0.3.25
243 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2ftrace-mapping cache-miss
244 silly fetch manifest collect-v8-coverage@^1.0.2
245 silly packumentCache full:https://registry.npmjs.org/collect-v8-coverage cache-miss
246 silly fetch manifest glob@^10.3.10
247 silly packumentCache full:https://registry.npmjs.org/glob cache-miss
248 silly fetch manifest istanbul-lib-coverage@^3.0.0
249 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-coverage cache-miss
250 silly fetch manifest istanbul-lib-instrument@^6.0.0
251 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-instrument cache-miss
252 silly fetch manifest istanbul-lib-report@^3.0.0
253 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-report cache-miss
254 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-coverage 40ms (cache miss)
255 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-coverage set size:undefined disposed:false
256 silly fetch manifest istanbul-lib-source-maps@^5.0.0
257 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-source-maps cache-miss
258 http fetch GET 200 https://registry.npmjs.org/glob 44ms (cache miss)
259 silly packumentCache full:https://registry.npmjs.org/glob set size:undefined disposed:false
260 silly fetch manifest istanbul-reports@^3.1.3
261 silly packumentCache full:https://registry.npmjs.org/istanbul-reports cache-miss
262 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-report 48ms (cache miss)
263 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-report set size:undefined disposed:false
264 silly fetch manifest jest-worker@30.2.0
265 silly packumentCache full:https://registry.npmjs.org/jest-worker cache-miss
266 http fetch GET 200 https://registry.npmjs.org/collect-v8-coverage 52ms (cache miss)
267 silly packumentCache full:https://registry.npmjs.org/collect-v8-coverage set size:undefined disposed:false
268 silly fetch manifest string-length@^4.0.2
269 silly packumentCache full:https://registry.npmjs.org/string-length cache-miss
270 http fetch GET 200 https://registry.npmjs.org/@jridgewell%2ftrace-mapping 53ms (cache miss)
271 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2ftrace-mapping set size:undefined disposed:false
272 silly fetch manifest v8-to-istanbul@^9.0.1
273 silly packumentCache full:https://registry.npmjs.org/v8-to-istanbul cache-miss
274 http fetch GET 200 https://registry.npmjs.org/@bcoe%2fv8-coverage 54ms (cache miss)
275 silly packumentCache full:https://registry.npmjs.org/@bcoe%2fv8-coverage set size:undefined disposed:false
276 silly fetch manifest @babel/core@^7.27.4
277 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore cache-miss
278 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-instrument 59ms (cache miss)
279 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-instrument set size:undefined disposed:false
280 silly fetch manifest babel-plugin-istanbul@^7.0.1
281 silly packumentCache full:https://registry.npmjs.org/babel-plugin-istanbul cache-miss
282 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-source-maps 37ms (cache miss)
283 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-source-maps set size:undefined disposed:false
284 silly fetch manifest convert-source-map@^2.0.0
285 silly packumentCache full:https://registry.npmjs.org/convert-source-map cache-miss
286 http fetch GET 200 https://registry.npmjs.org/string-length 32ms (cache miss)
287 silly packumentCache full:https://registry.npmjs.org/string-length set size:undefined disposed:false
288 silly fetch manifest fast-json-stable-stringify@^2.1.0
289 silly packumentCache full:https://registry.npmjs.org/fast-json-stable-stringify cache-miss
290 http fetch GET 200 https://registry.npmjs.org/istanbul-reports 40ms (cache miss)
291 silly packumentCache full:https://registry.npmjs.org/istanbul-reports set size:undefined disposed:false
292 silly fetch manifest pirates@^4.0.7
293 silly packumentCache full:https://registry.npmjs.org/pirates cache-miss
294 http fetch GET 200 https://registry.npmjs.org/jest-worker 40ms (cache miss)
295 silly packumentCache full:https://registry.npmjs.org/jest-worker set size:undefined disposed:false
296 silly fetch manifest write-file-atomic@^5.0.1
297 silly packumentCache full:https://registry.npmjs.org/write-file-atomic cache-miss
298 http fetch GET 200 https://registry.npmjs.org/@babel%2fcore 47ms (cache miss)
299 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore set size:undefined disposed:false
300 silly fetch manifest undici-types@~7.12.0
301 silly packumentCache full:https://registry.npmjs.org/undici-types cache-miss
302 http fetch GET 200 https://registry.npmjs.org/babel-plugin-istanbul 48ms (cache miss)
303 silly packumentCache full:https://registry.npmjs.org/babel-plugin-istanbul set size:undefined disposed:false
304 silly fetch manifest type-fest@^0.21.3
305 silly packumentCache full:https://registry.npmjs.org/type-fest cache-miss
306 http fetch GET 200 https://registry.npmjs.org/v8-to-istanbul 58ms (cache miss)
307 silly packumentCache full:https://registry.npmjs.org/v8-to-istanbul set size:undefined disposed:false
308 silly fetch manifest ansi-styles@^4.1.0
309 silly packumentCache full:https://registry.npmjs.org/ansi-styles cache-miss
310 http fetch GET 200 https://registry.npmjs.org/fast-json-stable-stringify 30ms (cache miss)
311 silly packumentCache full:https://registry.npmjs.org/fast-json-stable-stringify set size:undefined disposed:false
312 silly fetch manifest supports-color@^7.1.0
313 silly packumentCache full:https://registry.npmjs.org/supports-color cache-miss
314 http fetch GET 200 https://registry.npmjs.org/pirates 34ms (cache miss)
315 silly packumentCache full:https://registry.npmjs.org/pirates set size:undefined disposed:false
316 silly fetch manifest execa@^5.1.1
317 silly packumentCache full:https://registry.npmjs.org/execa cache-miss
318 http fetch GET 200 https://registry.npmjs.org/convert-source-map 42ms (cache miss)
319 silly packumentCache full:https://registry.npmjs.org/convert-source-map set size:undefined disposed:false
320 silly fetch manifest p-limit@^3.1.0
321 silly packumentCache full:https://registry.npmjs.org/p-limit cache-miss
322 http fetch GET 200 https://registry.npmjs.org/undici-types 29ms (cache miss)
323 silly packumentCache full:https://registry.npmjs.org/undici-types set size:undefined disposed:false
324 silly fetch manifest @jest/get-type@30.1.0
325 silly packumentCache full:https://registry.npmjs.org/@jest%2fget-type cache-miss
326 http fetch GET 200 https://registry.npmjs.org/write-file-atomic 50ms (cache miss)
327 silly packumentCache full:https://registry.npmjs.org/write-file-atomic set size:undefined disposed:false
328 silly fetch manifest @jest/test-sequencer@30.2.0
329 silly packumentCache full:https://registry.npmjs.org/@jest%2ftest-sequencer cache-miss
330 http fetch GET 200 https://registry.npmjs.org/supports-color 34ms (cache miss)
331 silly packumentCache full:https://registry.npmjs.org/supports-color set size:undefined disposed:false
332 silly fetch manifest babel-jest@30.2.0
333 silly packumentCache full:https://registry.npmjs.org/babel-jest cache-miss
334 http fetch GET 200 https://registry.npmjs.org/execa 35ms (cache miss)
335 silly packumentCache full:https://registry.npmjs.org/execa set size:undefined disposed:false
336 silly fetch manifest deepmerge@^4.3.1
337 silly packumentCache full:https://registry.npmjs.org/deepmerge cache-miss
338 http fetch GET 200 https://registry.npmjs.org/p-limit 36ms (cache miss)
339 silly packumentCache full:https://registry.npmjs.org/p-limit set size:undefined disposed:false
340 silly fetch manifest jest-circus@30.2.0
341 silly packumentCache full:https://registry.npmjs.org/jest-circus cache-miss
342 http fetch GET 200 https://registry.npmjs.org/ansi-styles 52ms (cache miss)
343 silly packumentCache full:https://registry.npmjs.org/ansi-styles set size:undefined disposed:false
344 silly fetch manifest jest-docblock@30.2.0
345 silly packumentCache full:https://registry.npmjs.org/jest-docblock cache-miss
346 http fetch GET 200 https://registry.npmjs.org/type-fest 77ms (cache miss)
347 silly packumentCache full:https://registry.npmjs.org/type-fest set size:undefined disposed:false
348 silly fetch manifest jest-environment-node@30.2.0
349 silly packumentCache full:https://registry.npmjs.org/jest-environment-node cache-miss
350 http fetch GET 200 https://registry.npmjs.org/@jest%2fget-type 56ms (cache miss)
351 silly packumentCache full:https://registry.npmjs.org/@jest%2fget-type set size:undefined disposed:false
352 silly fetch manifest parse-json@^5.2.0
353 silly packumentCache full:https://registry.npmjs.org/parse-json cache-miss
354 http fetch GET 200 https://registry.npmjs.org/deepmerge 38ms (cache miss)
355 silly packumentCache full:https://registry.npmjs.org/deepmerge set size:undefined disposed:false
356 silly fetch manifest strip-json-comments@^3.1.1
357 silly packumentCache full:https://registry.npmjs.org/strip-json-comments cache-miss
358 http fetch GET 200 https://registry.npmjs.org/babel-jest 48ms (cache miss)
359 silly packumentCache full:https://registry.npmjs.org/babel-jest set size:undefined disposed:false
360 silly fetch manifest anymatch@^3.1.3
361 silly packumentCache full:https://registry.npmjs.org/anymatch cache-miss
362 http fetch GET 200 https://registry.npmjs.org/@jest%2ftest-sequencer 61ms (cache miss)
363 silly packumentCache full:https://registry.npmjs.org/@jest%2ftest-sequencer set size:undefined disposed:false
364 silly fetch manifest fb-watchman@^2.0.2
365 silly packumentCache full:https://registry.npmjs.org/fb-watchman cache-miss
366 http fetch GET 200 https://registry.npmjs.org/jest-docblock 49ms (cache miss)
367 silly packumentCache full:https://registry.npmjs.org/jest-docblock set size:undefined disposed:false
368 silly fetch manifest walker@^1.0.8
369 silly packumentCache full:https://registry.npmjs.org/walker cache-miss
370 http fetch GET 200 https://registry.npmjs.org/parse-json 37ms (cache miss)
371 silly packumentCache full:https://registry.npmjs.org/parse-json set size:undefined disposed:false
372 silly fetch manifest fsevents@^2.3.3
373 silly packumentCache full:https://registry.npmjs.org/fsevents cache-miss
374 http fetch GET 200 https://registry.npmjs.org/jest-circus 70ms (cache miss)
375 silly packumentCache full:https://registry.npmjs.org/jest-circus set size:undefined disposed:false
376 silly fetch manifest @babel/code-frame@^7.27.1
377 silly packumentCache full:https://registry.npmjs.org/@babel%2fcode-frame cache-miss
378 http fetch GET 200 https://registry.npmjs.org/strip-json-comments 37ms (cache miss)
379 silly packumentCache full:https://registry.npmjs.org/strip-json-comments set size:undefined disposed:false
380 silly fetch manifest @types/stack-utils@^2.0.3
381 silly packumentCache full:https://registry.npmjs.org/@types%2fstack-utils cache-miss
382 http fetch GET 200 https://registry.npmjs.org/anymatch 36ms (cache miss)
383 silly packumentCache full:https://registry.npmjs.org/anymatch set size:undefined disposed:false
384 silly fetch manifest stack-utils@^2.0.6
385 silly packumentCache full:https://registry.npmjs.org/stack-utils cache-miss
386 http fetch GET 200 https://registry.npmjs.org/fb-watchman 37ms (cache miss)
387 silly packumentCache full:https://registry.npmjs.org/fb-watchman set size:undefined disposed:false
388 silly fetch manifest jest-pnp-resolver@^1.2.3
389 silly packumentCache full:https://registry.npmjs.org/jest-pnp-resolver cache-miss
390 http fetch GET 200 https://registry.npmjs.org/jest-environment-node 55ms (cache miss)
391 silly packumentCache full:https://registry.npmjs.org/jest-environment-node set size:undefined disposed:false
392 silly fetch manifest unrs-resolver@^1.7.11
393 silly packumentCache full:https://registry.npmjs.org/unrs-resolver cache-miss
394 http fetch GET 200 https://registry.npmjs.org/walker 42ms (cache miss)
395 silly packumentCache full:https://registry.npmjs.org/walker set size:undefined disposed:false
396 silly fetch manifest @jest/environment@30.2.0
397 silly packumentCache full:https://registry.npmjs.org/@jest%2fenvironment cache-miss
398 http fetch GET 200 https://registry.npmjs.org/@types%2fstack-utils 39ms (cache miss)
399 silly packumentCache full:https://registry.npmjs.org/@types%2fstack-utils set size:undefined disposed:false
400 silly fetch manifest emittery@^0.13.1
401 silly packumentCache full:https://registry.npmjs.org/emittery cache-miss
402 http fetch GET 200 https://registry.npmjs.org/stack-utils 41ms (cache miss)
403 silly packumentCache full:https://registry.npmjs.org/stack-utils set size:undefined disposed:false
404 silly fetch manifest jest-leak-detector@30.2.0
405 silly packumentCache full:https://registry.npmjs.org/jest-leak-detector cache-miss
406 http fetch GET 200 https://registry.npmjs.org/fsevents 49ms (cache miss)
407 silly packumentCache full:https://registry.npmjs.org/fsevents set size:undefined disposed:false
408 silly fetch manifest source-map-support@0.5.13
409 silly packumentCache full:https://registry.npmjs.org/source-map-support cache-miss
410 http fetch GET 200 https://registry.npmjs.org/@babel%2fcode-frame 48ms (cache miss)
411 silly packumentCache full:https://registry.npmjs.org/@babel%2fcode-frame set size:undefined disposed:false
412 silly fetch manifest @jest/environment@30.2.0
413 silly packumentCache full:https://registry.npmjs.org/@jest%2fenvironment cache-miss
414 http fetch GET 200 https://registry.npmjs.org/jest-pnp-resolver 43ms (cache miss)
415 silly packumentCache full:https://registry.npmjs.org/jest-pnp-resolver set size:undefined disposed:false
416 silly fetch manifest @jest/fake-timers@30.2.0
417 silly packumentCache full:https://registry.npmjs.org/@jest%2ffake-timers cache-miss
418 http fetch GET 200 https://registry.npmjs.org/unrs-resolver 45ms (cache miss)
419 silly packumentCache full:https://registry.npmjs.org/unrs-resolver set size:undefined disposed:false
420 silly fetch manifest @jest/globals@30.2.0
421 silly packumentCache full:https://registry.npmjs.org/@jest%2fglobals cache-miss
422 http fetch GET 200 https://registry.npmjs.org/emittery 33ms (cache miss)
423 silly packumentCache full:https://registry.npmjs.org/emittery set size:undefined disposed:false
424 silly fetch manifest @jest/source-map@30.0.1
425 silly packumentCache full:https://registry.npmjs.org/@jest%2fsource-map cache-miss
426 http fetch GET 200 https://registry.npmjs.org/jest-leak-detector 39ms (cache miss)
427 silly packumentCache full:https://registry.npmjs.org/jest-leak-detector set size:undefined disposed:false
428 silly fetch manifest cjs-module-lexer@^2.1.0
429 silly packumentCache full:https://registry.npmjs.org/cjs-module-lexer cache-miss
430 http fetch GET 200 https://registry.npmjs.org/source-map-support 42ms (cache miss)
431 silly packumentCache full:https://registry.npmjs.org/source-map-support set size:undefined disposed:false
432 silly fetch manifest jest-mock@30.2.0
433 silly packumentCache full:https://registry.npmjs.org/jest-mock cache-miss
434 http fetch GET 200 https://registry.npmjs.org/@jest%2fenvironment 55ms (cache miss)
435 silly packumentCache full:https://registry.npmjs.org/@jest%2fenvironment set size:undefined disposed:false
436 silly fetch manifest strip-bom@^4.0.0
437 silly packumentCache full:https://registry.npmjs.org/strip-bom cache-miss
438 http fetch GET 200 https://registry.npmjs.org/@jest%2fenvironment 80ms (cache miss)
439 silly packumentCache full:https://registry.npmjs.org/@jest%2fenvironment set size:undefined disposed:false
440 silly fetch manifest @babel/generator@^7.27.5
441 silly packumentCache full:https://registry.npmjs.org/@babel%2fgenerator cache-miss
442 http fetch GET 200 https://registry.npmjs.org/@jest%2ffake-timers 64ms (cache miss)
443 silly packumentCache full:https://registry.npmjs.org/@jest%2ffake-timers set size:undefined disposed:false
444 silly fetch manifest @babel/plugin-syntax-jsx@^7.27.1
445 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-jsx cache-miss
446 http fetch GET 200 https://registry.npmjs.org/@jest%2fglobals 57ms (cache miss)
447 silly packumentCache full:https://registry.npmjs.org/@jest%2fglobals set size:undefined disposed:false
448 silly fetch manifest @babel/plugin-syntax-typescript@^7.27.1
449 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-typescript cache-miss
450 http fetch GET 200 https://registry.npmjs.org/cjs-module-lexer 37ms (cache miss)
451 silly packumentCache full:https://registry.npmjs.org/cjs-module-lexer set size:undefined disposed:false
452 silly fetch manifest @babel/types@^7.27.3
453 silly packumentCache full:https://registry.npmjs.org/@babel%2ftypes cache-miss
454 http fetch GET 200 https://registry.npmjs.org/@jest%2fsource-map 52ms (cache miss)
455 silly packumentCache full:https://registry.npmjs.org/@jest%2fsource-map set size:undefined disposed:false
456 silly fetch manifest @jest/expect-utils@30.2.0
457 silly packumentCache full:https://registry.npmjs.org/@jest%2fexpect-utils cache-miss
458 http fetch GET 200 https://registry.npmjs.org/strip-bom 34ms (cache miss)
459 silly packumentCache full:https://registry.npmjs.org/strip-bom set size:undefined disposed:false
460 silly fetch manifest @jest/snapshot-utils@30.2.0
461 silly packumentCache full:https://registry.npmjs.org/@jest%2fsnapshot-utils cache-miss
462 http fetch GET 200 https://registry.npmjs.org/jest-mock 63ms (cache miss)
463 silly packumentCache full:https://registry.npmjs.org/jest-mock set size:undefined disposed:false
464 silly fetch manifest babel-preset-current-node-syntax@^1.2.0
465 silly packumentCache full:https://registry.npmjs.org/babel-preset-current-node-syntax cache-miss
466 http fetch GET 200 https://registry.npmjs.org/@babel%2ftypes 57ms (cache miss)
467 silly packumentCache full:https://registry.npmjs.org/@babel%2ftypes set size:undefined disposed:false
468 silly fetch manifest expect@30.2.0
469 silly packumentCache full:https://registry.npmjs.org/expect cache-miss
470 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-typescript 63ms (cache miss)
471 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-typescript set size:undefined disposed:false
472 silly fetch manifest jest-diff@30.2.0
473 silly packumentCache full:https://registry.npmjs.org/jest-diff cache-miss
474 http fetch GET 200 https://registry.npmjs.org/@jest%2fexpect-utils 57ms (cache miss)
475 silly packumentCache full:https://registry.npmjs.org/@jest%2fexpect-utils set size:undefined disposed:false
476 silly fetch manifest jest-matcher-utils@30.2.0
477 silly packumentCache full:https://registry.npmjs.org/jest-matcher-utils cache-miss
478 http fetch GET 200 https://registry.npmjs.org/@jest%2fsnapshot-utils 50ms (cache miss)
479 silly packumentCache full:https://registry.npmjs.org/@jest%2fsnapshot-utils set size:undefined disposed:false
480 silly fetch manifest semver@^7.7.2
481 silly packumentCache full:https://registry.npmjs.org/semver cache-miss
482 http fetch GET 200 https://registry.npmjs.org/babel-preset-current-node-syntax 38ms (cache miss)
483 silly packumentCache full:https://registry.npmjs.org/babel-preset-current-node-syntax set size:undefined disposed:false
484 silly fetch manifest synckit@^0.11.8
485 silly packumentCache full:https://registry.npmjs.org/synckit cache-miss
486 http fetch GET 200 https://registry.npmjs.org/@babel%2fgenerator 87ms (cache miss)
487 silly packumentCache full:https://registry.npmjs.org/@babel%2fgenerator set size:undefined disposed:false
488 silly fetch manifest picomatch@^4.0.2
489 silly packumentCache full:https://registry.npmjs.org/picomatch cache-miss
490 http fetch GET 200 https://registry.npmjs.org/expect 46ms (cache miss)
491 silly packumentCache full:https://registry.npmjs.org/expect set size:undefined disposed:false
492 silly fetch manifest camelcase@^6.3.0
493 silly packumentCache full:https://registry.npmjs.org/camelcase cache-miss
494 http fetch GET 200 https://registry.npmjs.org/synckit 44ms (cache miss)
495 silly packumentCache full:https://registry.npmjs.org/synckit set size:undefined disposed:false
496 silly fetch manifest leven@^3.1.0
497 silly packumentCache full:https://registry.npmjs.org/leven cache-miss
498 http fetch GET 200 https://registry.npmjs.org/jest-diff 56ms (cache miss)
499 silly packumentCache full:https://registry.npmjs.org/jest-diff set size:undefined disposed:false
500 silly fetch manifest braces@^3.0.3
501 silly packumentCache full:https://registry.npmjs.org/braces cache-miss
502 http fetch GET 200 https://registry.npmjs.org/jest-matcher-utils 57ms (cache miss)
503 silly packumentCache full:https://registry.npmjs.org/jest-matcher-utils set size:undefined disposed:false
504 silly fetch manifest picomatch@^2.3.1
505 silly packumentCache full:https://registry.npmjs.org/picomatch cache-miss
506 http fetch GET 200 https://registry.npmjs.org/semver 58ms (cache miss)
507 silly packumentCache full:https://registry.npmjs.org/semver set size:undefined disposed:false
508 silly fetch manifest ansi-styles@^5.2.0
509 silly packumentCache full:https://registry.npmjs.org/ansi-styles cache-miss
510 http cache https://registry.npmjs.org/ansi-styles 3ms (cache hit)
511 silly packumentCache full:https://registry.npmjs.org/ansi-styles set size:63233 disposed:false
512 silly fetch manifest react-is@^18.3.1
513 silly packumentCache full:https://registry.npmjs.org/react-is cache-miss
514 http fetch GET 200 https://registry.npmjs.org/picomatch 58ms (cache miss)
515 silly packumentCache full:https://registry.npmjs.org/picomatch set size:undefined disposed:false
516 http fetch GET 200 https://registry.npmjs.org/camelcase 29ms (cache miss)
517 silly packumentCache full:https://registry.npmjs.org/camelcase set size:undefined disposed:false
518 http fetch GET 200 https://registry.npmjs.org/leven 33ms (cache miss)
519 silly packumentCache full:https://registry.npmjs.org/leven set size:undefined disposed:false
520 http fetch GET 200 https://registry.npmjs.org/braces 36ms (cache miss)
521 silly packumentCache full:https://registry.npmjs.org/braces set size:undefined disposed:false
522 http fetch GET 200 https://registry.npmjs.org/picomatch 40ms (cache miss)
523 silly packumentCache full:https://registry.npmjs.org/picomatch set size:undefined disposed:false
524 http fetch GET 200 https://registry.npmjs.org/react-is 121ms (cache miss)
525 silly packumentCache full:https://registry.npmjs.org/react-is set size:undefined disposed:false
526 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-jsx 592ms (cache miss)
527 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-jsx set size:undefined disposed:false
528 silly placeDep ROOT @bcoe/v8-coverage@0.2.3 OK for: @jest/reporters@30.2.0 want: ^0.2.3
529 silly placeDep ROOT @jridgewell/trace-mapping@0.3.31 OK for: @jest/reporters@30.2.0 want: ^0.3.25
530 silly placeDep ROOT collect-v8-coverage@1.0.2 OK for: @jest/reporters@30.2.0 want: ^1.0.2
531 silly placeDep ROOT glob@10.4.5 OK for: @jest/reporters@30.2.0 want: ^10.3.10
532 silly placeDep ROOT istanbul-lib-coverage@3.2.2 OK for: @jest/reporters@30.2.0 want: ^3.0.0
533 silly placeDep ROOT istanbul-lib-instrument@6.0.3 OK for: @jest/reporters@30.2.0 want: ^6.0.0
534 silly placeDep ROOT istanbul-lib-report@3.0.1 OK for: @jest/reporters@30.2.0 want: ^3.0.0
535 silly placeDep ROOT istanbul-lib-source-maps@5.0.6 OK for: @jest/reporters@30.2.0 want: ^5.0.0
536 silly placeDep ROOT istanbul-reports@3.2.0 OK for: @jest/reporters@30.2.0 want: ^3.1.3
537 silly placeDep ROOT jest-worker@30.2.0 OK for: @jest/reporters@30.2.0 want: 30.2.0
538 silly placeDep ROOT string-length@4.0.2 OK for: @jest/reporters@30.2.0 want: ^4.0.2
539 silly placeDep ROOT v8-to-istanbul@9.3.0 OK for: @jest/reporters@30.2.0 want: ^9.0.1
540 silly fetch manifest @jridgewell/resolve-uri@^3.1.0
541 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fresolve-uri cache-miss
542 silly fetch manifest @jridgewell/sourcemap-codec@^1.4.14
543 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fsourcemap-codec cache-miss
544 silly fetch manifest minipass@^7.1.2
545 silly packumentCache full:https://registry.npmjs.org/minipass cache-miss
546 silly fetch manifest jackspeak@^3.1.2
547 silly packumentCache full:https://registry.npmjs.org/jackspeak cache-miss
548 silly fetch manifest minimatch@^9.0.4
549 silly packumentCache full:https://registry.npmjs.org/minimatch cache-miss
550 silly fetch manifest path-scurry@^1.11.1
551 silly packumentCache full:https://registry.npmjs.org/path-scurry cache-miss
552 silly fetch manifest foreground-child@^3.1.0
553 silly packumentCache full:https://registry.npmjs.org/foreground-child cache-miss
554 http fetch GET 200 https://registry.npmjs.org/jackspeak 36ms (cache miss)
555 silly packumentCache full:https://registry.npmjs.org/jackspeak set size:undefined disposed:false
556 silly fetch manifest package-json-from-dist@^1.0.0
557 silly packumentCache full:https://registry.npmjs.org/package-json-from-dist cache-miss
558 http fetch GET 200 https://registry.npmjs.org/path-scurry 46ms (cache miss)
559 silly packumentCache full:https://registry.npmjs.org/path-scurry set size:undefined disposed:false
560 silly fetch manifest @babel/core@^7.23.9
561 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore cache-miss
562 http fetch GET 200 https://registry.npmjs.org/minipass 49ms (cache miss)
563 silly packumentCache full:https://registry.npmjs.org/minipass set size:undefined disposed:false
564 silly fetch manifest @babel/parser@^7.23.9
565 silly packumentCache full:https://registry.npmjs.org/@babel%2fparser cache-miss
566 http cache https://registry.npmjs.org/@babel%2fcore 4ms (cache hit)
567 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore set size:731531 disposed:false
568 silly fetch manifest @istanbuljs/schema@^0.1.3
569 silly packumentCache full:https://registry.npmjs.org/@istanbuljs%2fschema cache-miss
570 http fetch GET 200 https://registry.npmjs.org/minimatch 54ms (cache miss)
571 silly packumentCache full:https://registry.npmjs.org/minimatch set size:undefined disposed:false
572 silly fetch manifest semver@^7.5.4
573 silly packumentCache full:https://registry.npmjs.org/semver cache-miss
574 http fetch GET 200 https://registry.npmjs.org/@jridgewell%2fsourcemap-codec 57ms (cache miss)
575 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fsourcemap-codec set size:undefined disposed:false
576 silly fetch manifest make-dir@^4.0.0
577 silly packumentCache full:https://registry.npmjs.org/make-dir cache-miss
578 http cache https://registry.npmjs.org/semver 2ms (cache hit)
579 silly packumentCache full:https://registry.npmjs.org/semver set size:224186 disposed:false
580 silly fetch manifest debug@^4.1.1
581 silly packumentCache full:https://registry.npmjs.org/debug cache-miss
582 http fetch GET 200 https://registry.npmjs.org/package-json-from-dist 35ms (cache miss)
583 silly packumentCache full:https://registry.npmjs.org/package-json-from-dist set size:undefined disposed:false
584 silly fetch manifest html-escaper@^2.0.0
585 silly packumentCache full:https://registry.npmjs.org/html-escaper cache-miss
586 http fetch GET 200 https://registry.npmjs.org/@jridgewell%2fresolve-uri 75ms (cache miss)
587 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fresolve-uri set size:undefined disposed:false
588 silly fetch manifest @ungap/structured-clone@^1.3.0
589 silly packumentCache full:https://registry.npmjs.org/@ungap%2fstructured-clone cache-miss
590 http fetch GET 200 https://registry.npmjs.org/foreground-child 75ms (cache miss)
591 silly packumentCache full:https://registry.npmjs.org/foreground-child set size:undefined disposed:false
592 silly fetch manifest merge-stream@^2.0.0
593 silly packumentCache full:https://registry.npmjs.org/merge-stream cache-miss
594 http fetch GET 200 https://registry.npmjs.org/make-dir 37ms (cache miss)
595 silly packumentCache full:https://registry.npmjs.org/make-dir set size:undefined disposed:false
596 silly fetch manifest supports-color@^8.1.1
597 silly packumentCache full:https://registry.npmjs.org/supports-color cache-miss
598 http cache https://registry.npmjs.org/supports-color 2ms (cache hit)
599 silly packumentCache full:https://registry.npmjs.org/supports-color set size:118680 disposed:false
600 silly fetch manifest char-regex@^1.0.2
601 silly packumentCache full:https://registry.npmjs.org/char-regex cache-miss
602 http fetch GET 200 https://registry.npmjs.org/@babel%2fparser 49ms (cache miss)
603 silly packumentCache full:https://registry.npmjs.org/@babel%2fparser set size:undefined disposed:false
604 silly fetch manifest strip-ansi@^6.0.0
605 silly packumentCache full:https://registry.npmjs.org/strip-ansi cache-miss
606 http fetch GET 200 https://registry.npmjs.org/debug 46ms (cache miss)
607 silly packumentCache full:https://registry.npmjs.org/debug set size:undefined disposed:false
608 silly fetch manifest @types/istanbul-lib-coverage@^2.0.1
609 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-coverage cache-miss
610 http cache https://registry.npmjs.org/@types%2fistanbul-lib-coverage 1ms (cache hit)
611 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-coverage set size:18110 disposed:false
612 http fetch GET 200 https://registry.npmjs.org/@istanbuljs%2fschema 55ms (cache miss)
613 silly packumentCache full:https://registry.npmjs.org/@istanbuljs%2fschema set size:undefined disposed:false
614 http fetch GET 200 https://registry.npmjs.org/merge-stream 39ms (cache miss)
615 silly packumentCache full:https://registry.npmjs.org/merge-stream set size:undefined disposed:false
616 http fetch GET 200 https://registry.npmjs.org/html-escaper 42ms (cache miss)
617 silly packumentCache full:https://registry.npmjs.org/html-escaper set size:undefined disposed:false
618 http fetch GET 200 https://registry.npmjs.org/char-regex 36ms (cache miss)
619 silly packumentCache full:https://registry.npmjs.org/char-regex set size:undefined disposed:false
620 http fetch GET 200 https://registry.npmjs.org/@ungap%2fstructured-clone 60ms (cache miss)
621 silly packumentCache full:https://registry.npmjs.org/@ungap%2fstructured-clone set size:undefined disposed:false
622 http fetch GET 200 https://registry.npmjs.org/strip-ansi 32ms (cache miss)
623 silly packumentCache full:https://registry.npmjs.org/strip-ansi set size:undefined disposed:false
624 silly placeDep ROOT @types/istanbul-lib-coverage@2.0.6 OK for: @jest/test-result@30.2.0 want: ^2.0.6
625 silly placeDep ROOT @babel/core@7.28.4 OK for: @jest/transform@30.2.0 want: ^7.27.4
626 silly placeDep ROOT babel-plugin-istanbul@7.0.1 OK for: @jest/transform@30.2.0 want: ^7.0.1
627 silly placeDep ROOT convert-source-map@2.0.0 OK for: @jest/transform@30.2.0 want: ^2.0.0
628 silly placeDep ROOT fast-json-stable-stringify@2.1.0 OK for: @jest/transform@30.2.0 want: ^2.1.0
629 silly placeDep ROOT pirates@4.0.7 OK for: @jest/transform@30.2.0 want: ^4.0.7
630 silly placeDep ROOT write-file-atomic@5.0.1 OK for: @jest/transform@30.2.0 want: ^5.0.1
631 silly fetch manifest debug@^4.1.0
632 silly packumentCache full:https://registry.npmjs.org/debug cache-miss
633 silly fetch manifest json5@^2.2.3
634 silly packumentCache full:https://registry.npmjs.org/json5 cache-miss
635 silly fetch manifest semver@^6.3.1
636 silly packumentCache full:https://registry.npmjs.org/semver cache-hit
637 silly fetch manifest gensync@^1.0.0-beta.2
638 silly packumentCache full:https://registry.npmjs.org/gensync cache-miss
639 silly fetch manifest @babel/types@^7.28.4
640 silly packumentCache full:https://registry.npmjs.org/@babel%2ftypes cache-miss
641 silly fetch manifest @babel/parser@^7.28.4
642 silly packumentCache full:https://registry.npmjs.org/@babel%2fparser cache-miss
643 silly fetch manifest @babel/helpers@^7.28.4
644 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelpers cache-miss
645 silly fetch manifest @babel/template@^7.27.2
646 silly packumentCache full:https://registry.npmjs.org/@babel%2ftemplate cache-miss
647 http cache https://registry.npmjs.org/debug 4ms (cache hit)
648 silly packumentCache full:https://registry.npmjs.org/debug set size:195840 disposed:false
649 silly fetch manifest @babel/traverse@^7.28.4
650 silly packumentCache full:https://registry.npmjs.org/@babel%2ftraverse cache-miss
651 http cache https://registry.npmjs.org/@babel%2ftypes 6ms (cache hit)
652 silly packumentCache full:https://registry.npmjs.org/@babel%2ftypes set size:471681 disposed:false
653 silly fetch manifest @babel/generator@^7.28.3
654 silly packumentCache full:https://registry.npmjs.org/@babel%2fgenerator cache-miss
655 http cache https://registry.npmjs.org/@babel%2fparser 8ms (cache hit)
656 silly packumentCache full:https://registry.npmjs.org/@babel%2fparser set size:698218 disposed:false
657 silly fetch manifest @jridgewell/remapping@^2.3.5
658 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fremapping cache-miss
659 http cache https://registry.npmjs.org/@babel%2fgenerator 6ms (cache hit)
660 silly packumentCache full:https://registry.npmjs.org/@babel%2fgenerator set size:488039 disposed:false
661 silly fetch manifest @babel/helper-module-transforms@^7.28.3
662 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-module-transforms cache-miss
663 http fetch GET 200 https://registry.npmjs.org/json5 37ms (cache miss)
664 silly packumentCache full:https://registry.npmjs.org/json5 set size:undefined disposed:false
665 silly fetch manifest @babel/helper-compilation-targets@^7.27.2
666 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-compilation-targets cache-miss
667 http fetch GET 200 https://registry.npmjs.org/gensync 48ms (cache miss)
668 silly packumentCache full:https://registry.npmjs.org/gensync set size:undefined disposed:false
669 silly fetch manifest @babel/helper-plugin-utils@^7.0.0
670 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-plugin-utils cache-miss
671 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelpers 51ms (cache miss)
672 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelpers set size:undefined disposed:false
673 silly fetch manifest @istanbuljs/load-nyc-config@^1.0.0
674 silly packumentCache full:https://registry.npmjs.org/@istanbuljs%2fload-nyc-config cache-miss
675 http fetch GET 200 https://registry.npmjs.org/@jridgewell%2fremapping 52ms (cache miss)
676 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fremapping set size:undefined disposed:false
677 silly fetch manifest test-exclude@^6.0.0
678 silly packumentCache full:https://registry.npmjs.org/test-exclude cache-miss
679 http fetch GET 200 https://registry.npmjs.org/@babel%2ftraverse 60ms (cache miss)
680 silly packumentCache full:https://registry.npmjs.org/@babel%2ftraverse set size:undefined disposed:false
681 silly fetch manifest imurmurhash@^0.1.4
682 silly packumentCache full:https://registry.npmjs.org/imurmurhash cache-miss
683 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-module-transforms 53ms (cache miss)
684 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-module-transforms set size:undefined disposed:false
685 silly fetch manifest signal-exit@^4.0.1
686 silly packumentCache full:https://registry.npmjs.org/signal-exit cache-miss
687 http fetch GET 200 https://registry.npmjs.org/@babel%2ftemplate 73ms (cache miss)
688 silly packumentCache full:https://registry.npmjs.org/@babel%2ftemplate set size:undefined disposed:false
689 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-compilation-targets 52ms (cache miss)
690 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-compilation-targets set size:undefined disposed:false
691 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-plugin-utils 50ms (cache miss)
692 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-plugin-utils set size:undefined disposed:false
693 http fetch GET 200 https://registry.npmjs.org/test-exclude 36ms (cache miss)
694 silly packumentCache full:https://registry.npmjs.org/test-exclude set size:undefined disposed:false
695 http fetch GET 200 https://registry.npmjs.org/@istanbuljs%2fload-nyc-config 57ms (cache miss)
696 silly packumentCache full:https://registry.npmjs.org/@istanbuljs%2fload-nyc-config set size:undefined disposed:false
697 http fetch GET 200 https://registry.npmjs.org/imurmurhash 43ms (cache miss)
698 silly packumentCache full:https://registry.npmjs.org/imurmurhash set size:undefined disposed:false
699 http fetch GET 200 https://registry.npmjs.org/signal-exit 42ms (cache miss)
700 silly packumentCache full:https://registry.npmjs.org/signal-exit set size:undefined disposed:false
701 silly fetch manifest @babel/core@^7.0.0
702 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore cache-hit
703 silly placeDep ROOT @babel/code-frame@7.27.1 OK for: @babel/core@7.28.4 want: ^7.27.1
704 silly placeDep ROOT @babel/generator@7.28.3 OK for: @babel/core@7.28.4 want: ^7.28.3
705 silly placeDep ROOT @babel/helper-compilation-targets@7.27.2 OK for: @babel/core@7.28.4 want: ^7.27.2
706 silly placeDep ROOT @babel/helper-module-transforms@7.28.3 OK for: @babel/core@7.28.4 want: ^7.28.3
707 silly placeDep ROOT @babel/helpers@7.28.4 OK for: @babel/core@7.28.4 want: ^7.28.4
708 silly placeDep ROOT @babel/parser@7.28.4 OK for: @babel/core@7.28.4 want: ^7.28.4
709 silly placeDep ROOT @babel/template@7.27.2 OK for: @babel/core@7.28.4 want: ^7.27.2
710 silly placeDep ROOT @babel/traverse@7.28.4 OK for: @babel/core@7.28.4 want: ^7.28.4
711 silly placeDep ROOT @babel/types@7.28.4 OK for: @babel/core@7.28.4 want: ^7.28.4
712 silly placeDep ROOT @jridgewell/remapping@2.3.5 OK for: @babel/core@7.28.4 want: ^2.3.5
713 silly placeDep ROOT debug@4.4.3 OK for: @babel/core@7.28.4 want: ^4.1.0
714 silly placeDep ROOT gensync@1.0.0-beta.2 OK for: @babel/core@7.28.4 want: ^1.0.0-beta.2
715 silly placeDep ROOT json5@2.2.3 OK for: @babel/core@7.28.4 want: ^2.2.3
716 silly placeDep ROOT semver@6.3.1 OK for: @babel/core@7.28.4 want: ^6.3.1
717 silly fetch manifest js-tokens@^4.0.0
718 silly packumentCache full:https://registry.npmjs.org/js-tokens cache-miss
719 silly fetch manifest picocolors@^1.1.1
720 silly packumentCache full:https://registry.npmjs.org/picocolors cache-miss
721 silly fetch manifest @babel/helper-validator-identifier@^7.27.1
722 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-validator-identifier cache-miss
723 silly fetch manifest jsesc@^3.0.2
724 silly packumentCache full:https://registry.npmjs.org/jsesc cache-miss
725 silly fetch manifest @babel/types@^7.28.2
726 silly packumentCache full:https://registry.npmjs.org/@babel%2ftypes cache-hit
727 silly fetch manifest @babel/parser@^7.28.3
728 silly packumentCache full:https://registry.npmjs.org/@babel%2fparser cache-hit
729 silly fetch manifest @jridgewell/gen-mapping@^0.3.12
730 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fgen-mapping cache-miss
731 silly fetch manifest lru-cache@^5.1.1
732 silly packumentCache full:https://registry.npmjs.org/lru-cache cache-miss
733 silly fetch manifest browserslist@^4.24.0
734 silly packumentCache full:https://registry.npmjs.org/browserslist cache-miss
735 http fetch GET 200 https://registry.npmjs.org/jsesc 37ms (cache miss)
736 silly packumentCache full:https://registry.npmjs.org/jsesc set size:undefined disposed:false
737 silly fetch manifest @babel/compat-data@^7.27.2
738 silly packumentCache full:https://registry.npmjs.org/@babel%2fcompat-data cache-miss
739 http fetch GET 200 https://registry.npmjs.org/picocolors 41ms (cache miss)
740 silly packumentCache full:https://registry.npmjs.org/picocolors set size:undefined disposed:false
741 silly fetch manifest @babel/helper-validator-option@^7.27.1
742 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-validator-option cache-miss
743 http fetch GET 200 https://registry.npmjs.org/js-tokens 49ms (cache miss)
744 silly packumentCache full:https://registry.npmjs.org/js-tokens set size:undefined disposed:false
745 silly fetch manifest @babel/traverse@^7.28.3
746 silly packumentCache full:https://registry.npmjs.org/@babel%2ftraverse cache-miss
747 http fetch GET 200 https://registry.npmjs.org/lru-cache 49ms (cache miss)
748 silly packumentCache full:https://registry.npmjs.org/lru-cache set size:undefined disposed:false
749 silly fetch manifest @babel/helper-module-imports@^7.27.1
750 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-module-imports cache-miss
751 http cache https://registry.npmjs.org/@babel%2ftraverse 7ms (cache hit)
752 silly packumentCache full:https://registry.npmjs.org/@babel%2ftraverse set size:531401 disposed:false
753 silly fetch manifest @babel/helper-validator-identifier@^7.27.1
754 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-validator-identifier cache-miss
755 http fetch GET 200 https://registry.npmjs.org/browserslist 58ms (cache miss)
756 silly packumentCache full:https://registry.npmjs.org/browserslist set size:undefined disposed:false
757 silly fetch manifest @babel/types@^7.27.1
758 silly packumentCache full:https://registry.npmjs.org/@babel%2ftypes cache-hit
759 silly fetch manifest debug@^4.3.1
760 silly packumentCache full:https://registry.npmjs.org/debug cache-hit
761 silly fetch manifest @babel/helper-globals@^7.28.0
762 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-globals cache-miss
763 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-validator-identifier 62ms (cache miss)
764 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-validator-identifier set size:undefined disposed:false
765 silly fetch manifest @babel/helper-string-parser@^7.27.1
766 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-string-parser cache-miss
767 http fetch GET 200 https://registry.npmjs.org/@jridgewell%2fgen-mapping 61ms (cache miss)
768 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fgen-mapping set size:undefined disposed:false
769 silly fetch manifest @jridgewell/gen-mapping@^0.3.5
770 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fgen-mapping cache-miss
771 http cache https://registry.npmjs.org/@jridgewell%2fgen-mapping 1ms (cache hit)
772 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fgen-mapping set size:80719 disposed:false
773 silly fetch manifest ms@^2.1.3
774 silly packumentCache full:https://registry.npmjs.org/ms cache-miss
775 http fetch GET 200 https://registry.npmjs.org/@babel%2fcompat-data 45ms (cache miss)
776 silly packumentCache full:https://registry.npmjs.org/@babel%2fcompat-data set size:undefined disposed:false
777 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-validator-option 46ms (cache miss)
778 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-validator-option set size:undefined disposed:false
779 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-module-imports 51ms (cache miss)
780 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-module-imports set size:undefined disposed:false
781 http fetch GET 200 https://registry.npmjs.org/ms 41ms (cache miss)
782 silly packumentCache full:https://registry.npmjs.org/ms set size:undefined disposed:false
783 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-globals 48ms (cache miss)
784 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-globals set size:undefined disposed:false
785 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-string-parser 47ms (cache miss)
786 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-string-parser set size:undefined disposed:false
787 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-validator-identifier 64ms (cache miss)
788 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-validator-identifier set size:undefined disposed:false
789 silly placeDep ROOT @babel/helper-validator-identifier@7.27.1 OK for: @babel/code-frame@7.27.1 want: ^7.27.1
790 silly placeDep ROOT js-tokens@4.0.0 OK for: @babel/code-frame@7.27.1 want: ^4.0.0
791 silly placeDep ROOT picocolors@1.1.1 OK for: @babel/code-frame@7.27.1 want: ^1.1.1
792 silly placeDep ROOT @jridgewell/gen-mapping@0.3.13 OK for: @babel/generator@7.28.3 want: ^0.3.12
793 silly placeDep ROOT jsesc@3.1.0 OK for: @babel/generator@7.28.3 want: ^3.0.2
794 silly fetch manifest @jridgewell/sourcemap-codec@^1.5.0
795 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fsourcemap-codec cache-miss
796 http cache https://registry.npmjs.org/@jridgewell%2fsourcemap-codec 1ms (cache hit)
797 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fsourcemap-codec set size:59275 disposed:false
798 silly placeDep ROOT @babel/compat-data@7.28.4 OK for: @babel/helper-compilation-targets@7.27.2 want: ^7.27.2
799 silly placeDep ROOT @babel/helper-validator-option@7.27.1 OK for: @babel/helper-compilation-targets@7.27.2 want: ^7.27.1
800 silly placeDep ROOT browserslist@4.26.2 OK for: @babel/helper-compilation-targets@7.27.2 want: ^4.24.0
801 silly placeDep ROOT lru-cache@5.1.1 OK for: @babel/helper-compilation-targets@7.27.2 want: ^5.1.1
802 silly fetch manifest baseline-browser-mapping@^2.8.3
803 silly packumentCache full:https://registry.npmjs.org/baseline-browser-mapping cache-miss
804 silly fetch manifest caniuse-lite@^1.0.30001741
805 silly packumentCache full:https://registry.npmjs.org/caniuse-lite cache-miss
806 silly fetch manifest electron-to-chromium@^1.5.218
807 silly packumentCache full:https://registry.npmjs.org/electron-to-chromium cache-miss
808 silly fetch manifest node-releases@^2.0.21
809 silly packumentCache full:https://registry.npmjs.org/node-releases cache-miss
810 silly fetch manifest update-browserslist-db@^1.1.3
811 silly packumentCache full:https://registry.npmjs.org/update-browserslist-db cache-miss
812 silly fetch manifest yallist@^3.0.2
813 silly packumentCache full:https://registry.npmjs.org/yallist cache-miss
814 http fetch GET 200 https://registry.npmjs.org/yallist 33ms (cache miss)
815 silly packumentCache full:https://registry.npmjs.org/yallist set size:undefined disposed:false
816 http fetch GET 200 https://registry.npmjs.org/baseline-browser-mapping 37ms (cache miss)
817 silly packumentCache full:https://registry.npmjs.org/baseline-browser-mapping set size:undefined disposed:false
818 http fetch GET 200 https://registry.npmjs.org/update-browserslist-db 39ms (cache miss)
819 silly packumentCache full:https://registry.npmjs.org/update-browserslist-db set size:undefined disposed:false
820 http fetch GET 200 https://registry.npmjs.org/node-releases 77ms (cache miss)
821 silly packumentCache full:https://registry.npmjs.org/node-releases set size:undefined disposed:false
822 http fetch GET 200 https://registry.npmjs.org/caniuse-lite 93ms (cache miss)
823 silly packumentCache full:https://registry.npmjs.org/caniuse-lite set size:undefined disposed:false
824 http fetch GET 200 https://registry.npmjs.org/electron-to-chromium 128ms (cache miss)
825 silly packumentCache full:https://registry.npmjs.org/electron-to-chromium set size:undefined disposed:false
826 silly placeDep ROOT @babel/helper-module-imports@7.27.1 OK for: @babel/helper-module-transforms@7.28.3 want: ^7.27.1
827 silly placeDep ROOT @babel/helper-globals@7.28.0 OK for: @babel/traverse@7.28.4 want: ^7.28.0
828 silly placeDep ROOT @babel/helper-string-parser@7.27.1 OK for: @babel/types@7.28.4 want: ^7.27.1
829 silly placeDep ROOT @jest/schemas@30.0.5 OK for: @jest/types@30.2.0 want: 30.0.5
830 silly placeDep ROOT @types/istanbul-reports@3.0.4 OK for: @jest/types@30.2.0 want: ^3.0.4
831 silly placeDep ROOT @types/yargs@17.0.33 OK for: @jest/types@30.2.0 want: ^17.0.33
832 silly fetch manifest @sinclair/typebox@^0.34.0
833 silly packumentCache full:https://registry.npmjs.org/@sinclair%2ftypebox cache-miss
834 silly fetch manifest @types/istanbul-lib-report@*
835 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-report cache-miss
836 silly fetch manifest @types/yargs-parser@*
837 silly packumentCache full:https://registry.npmjs.org/@types%2fyargs-parser cache-miss
838 http fetch GET 200 https://registry.npmjs.org/@types%2fyargs-parser 39ms (cache miss)
839 silly packumentCache full:https://registry.npmjs.org/@types%2fyargs-parser set size:undefined disposed:false
840 http fetch GET 200 https://registry.npmjs.org/@types%2fistanbul-lib-report 39ms (cache miss)
841 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-report set size:undefined disposed:false
842 http fetch GET 200 https://registry.npmjs.org/@sinclair%2ftypebox 112ms (cache miss)
843 silly packumentCache full:https://registry.npmjs.org/@sinclair%2ftypebox set size:undefined disposed:false
844 silly placeDep ROOT @sinclair/typebox@0.34.41 OK for: @jest/schemas@30.0.5 want: ^0.34.0
845 silly placeDep ROOT @jridgewell/sourcemap-codec@1.5.5 OK for: @jridgewell/gen-mapping@0.3.13 want: ^1.5.0
846 silly placeDep ROOT @jridgewell/resolve-uri@3.1.2 OK for: @jridgewell/trace-mapping@0.3.31 want: ^3.1.0
847 silly placeDep ROOT @types/istanbul-lib-report@3.0.3 OK for: @types/istanbul-reports@3.0.4 want: *
848 silly placeDep ROOT undici-types@7.12.0 OK for: @types/node@24.5.2 want: ~7.12.0
849 silly placeDep ROOT @types/yargs-parser@21.0.3 OK for: @types/yargs@17.0.33 want: *
850 silly placeDep ROOT type-fest@0.21.3 OK for: ansi-escapes@4.3.2 want: ^0.21.3
851 silly placeDep ROOT @babel/helper-plugin-utils@7.27.1 OK for: babel-plugin-istanbul@7.0.1 want: ^7.0.0
852 silly placeDep ROOT @istanbuljs/load-nyc-config@1.1.0 OK for: babel-plugin-istanbul@7.0.1 want: ^1.0.0
853 silly placeDep ROOT @istanbuljs/schema@0.1.3 OK for: babel-plugin-istanbul@7.0.1 want: ^0.1.3
854 silly placeDep ROOT test-exclude@6.0.0 OK for: babel-plugin-istanbul@7.0.1 want: ^6.0.0
855 silly fetch manifest camelcase@^5.3.1
856 silly packumentCache full:https://registry.npmjs.org/camelcase cache-miss
857 silly fetch manifest find-up@^4.1.0
858 silly packumentCache full:https://registry.npmjs.org/find-up cache-miss
859 silly fetch manifest get-package-type@^0.1.0
860 silly packumentCache full:https://registry.npmjs.org/get-package-type cache-miss
861 silly fetch manifest js-yaml@^3.13.1
862 silly packumentCache full:https://registry.npmjs.org/js-yaml cache-miss
863 silly fetch manifest resolve-from@^5.0.0
864 silly packumentCache full:https://registry.npmjs.org/resolve-from cache-miss
865 silly fetch manifest glob@^7.1.4
866 silly packumentCache full:https://registry.npmjs.org/glob cache-miss
867 silly fetch manifest minimatch@^3.0.4
868 silly packumentCache full:https://registry.npmjs.org/minimatch cache-miss
869 http cache https://registry.npmjs.org/camelcase 3ms (cache hit)
870 silly packumentCache full:https://registry.npmjs.org/camelcase set size:57059 disposed:false
871 http cache https://registry.npmjs.org/minimatch 4ms (cache hit)
872 silly packumentCache full:https://registry.npmjs.org/minimatch set size:298903 disposed:false
873 http cache https://registry.npmjs.org/glob 5ms (cache hit)
874 silly packumentCache full:https://registry.npmjs.org/glob set size:408718 disposed:false
875 http fetch GET 200 https://registry.npmjs.org/resolve-from 39ms (cache miss)
876 silly packumentCache full:https://registry.npmjs.org/resolve-from set size:undefined disposed:false
877 http fetch GET 200 https://registry.npmjs.org/find-up 41ms (cache miss)
878 silly packumentCache full:https://registry.npmjs.org/find-up set size:undefined disposed:false
879 http fetch GET 200 https://registry.npmjs.org/js-yaml 50ms (cache miss)
880 silly packumentCache full:https://registry.npmjs.org/js-yaml set size:undefined disposed:false
881 http fetch GET 200 https://registry.npmjs.org/get-package-type 67ms (cache miss)
882 silly packumentCache full:https://registry.npmjs.org/get-package-type set size:undefined disposed:false
883 silly placeDep ROOT camelcase@5.3.1 OK for: @istanbuljs/load-nyc-config@1.1.0 want: ^5.3.1
884 silly placeDep ROOT find-up@4.1.0 OK for: @istanbuljs/load-nyc-config@1.1.0 want: ^4.1.0
885 silly placeDep ROOT get-package-type@0.1.0 OK for: @istanbuljs/load-nyc-config@1.1.0 want: ^0.1.0
886 silly placeDep ROOT js-yaml@3.14.1 OK for: @istanbuljs/load-nyc-config@1.1.0 want: ^3.13.1
887 silly placeDep ROOT resolve-from@5.0.0 OK for: @istanbuljs/load-nyc-config@1.1.0 want: ^5.0.0
888 silly fetch manifest locate-path@^5.0.0
889 silly packumentCache full:https://registry.npmjs.org/locate-path cache-miss
890 silly fetch manifest path-exists@^4.0.0
891 silly packumentCache full:https://registry.npmjs.org/path-exists cache-miss
892 silly fetch manifest argparse@^1.0.7
893 silly packumentCache full:https://registry.npmjs.org/argparse cache-miss
894 silly fetch manifest esprima@^4.0.0
895 silly packumentCache full:https://registry.npmjs.org/esprima cache-miss
896 http fetch GET 200 https://registry.npmjs.org/path-exists 34ms (cache miss)
897 silly packumentCache full:https://registry.npmjs.org/path-exists set size:undefined disposed:false
898 http fetch GET 200 https://registry.npmjs.org/esprima 35ms (cache miss)
899 silly packumentCache full:https://registry.npmjs.org/esprima set size:undefined disposed:false
900 http fetch GET 200 https://registry.npmjs.org/locate-path 41ms (cache miss)
901 silly packumentCache full:https://registry.npmjs.org/locate-path set size:undefined disposed:false
902 http fetch GET 200 https://registry.npmjs.org/argparse 50ms (cache miss)
903 silly packumentCache full:https://registry.npmjs.org/argparse set size:undefined disposed:false
904 silly fetch manifest browserslist@>= 4.21.0
905 silly packumentCache full:https://registry.npmjs.org/browserslist cache-miss
906 http cache https://registry.npmjs.org/browserslist 2ms (cache hit)
907 silly packumentCache full:https://registry.npmjs.org/browserslist set size:494881 disposed:false
908 silly placeDep ROOT baseline-browser-mapping@2.8.8 OK for: browserslist@4.26.2 want: ^2.8.3
909 silly placeDep ROOT caniuse-lite@1.0.30001745 OK for: browserslist@4.26.2 want: ^1.0.30001741
910 silly placeDep ROOT electron-to-chromium@1.5.227 OK for: browserslist@4.26.2 want: ^1.5.218
911 silly placeDep ROOT node-releases@2.0.21 OK for: browserslist@4.26.2 want: ^2.0.21
912 silly placeDep ROOT update-browserslist-db@1.1.3 OK for: browserslist@4.26.2 want: ^1.1.3
913 silly fetch manifest escalade@^3.2.0
914 silly packumentCache full:https://registry.npmjs.org/escalade cache-miss
915 http fetch GET 200 https://registry.npmjs.org/escalade 35ms (cache miss)
916 silly packumentCache full:https://registry.npmjs.org/escalade set size:undefined disposed:false
917 silly placeDep ROOT ansi-styles@4.3.0 OK for: chalk@4.1.2 want: ^4.1.0
918 silly placeDep ROOT supports-color@7.2.0 OK for: chalk@4.1.2 want: ^7.1.0
919 silly fetch manifest color-convert@^2.0.1
920 silly packumentCache full:https://registry.npmjs.org/color-convert cache-miss
921 silly fetch manifest has-flag@^4.0.0
922 silly packumentCache full:https://registry.npmjs.org/has-flag cache-miss
923 http fetch GET 200 https://registry.npmjs.org/has-flag 40ms (cache miss)
924 silly packumentCache full:https://registry.npmjs.org/has-flag set size:undefined disposed:false
925 http fetch GET 200 https://registry.npmjs.org/color-convert 40ms (cache miss)
926 silly packumentCache full:https://registry.npmjs.org/color-convert set size:undefined disposed:false
927 silly placeDep ROOT color-convert@2.0.1 OK for: ansi-styles@4.3.0 want: ^2.0.1
928 silly fetch manifest color-name@~1.1.4
929 silly packumentCache full:https://registry.npmjs.org/color-name cache-miss
930 http fetch GET 200 https://registry.npmjs.org/color-name 147ms (cache miss)
931 silly packumentCache full:https://registry.npmjs.org/color-name set size:undefined disposed:false
932 silly placeDep ROOT color-name@1.1.4 OK for: color-convert@2.0.1 want: ~1.1.4
933 silly placeDep ROOT ms@2.1.3 OK for: debug@4.4.3 want: ^2.1.3
934 silly placeDep ROOT locate-path@5.0.0 OK for: find-up@4.1.0 want: ^5.0.0
935 silly placeDep ROOT path-exists@4.0.0 OK for: find-up@4.1.0 want: ^4.0.0
936 silly fetch manifest p-locate@^4.1.0
937 silly packumentCache full:https://registry.npmjs.org/p-locate cache-miss
938 http fetch GET 200 https://registry.npmjs.org/p-locate 69ms (cache miss)
939 silly packumentCache full:https://registry.npmjs.org/p-locate set size:undefined disposed:false
940 silly placeDep ROOT foreground-child@3.3.1 OK for: glob@10.4.5 want: ^3.1.0
941 silly placeDep ROOT jackspeak@3.4.3 OK for: glob@10.4.5 want: ^3.1.2
942 silly placeDep ROOT minimatch@9.0.5 OK for: glob@10.4.5 want: ^9.0.4
943 silly placeDep ROOT minipass@7.1.2 OK for: glob@10.4.5 want: ^7.1.2
944 silly placeDep ROOT package-json-from-dist@1.0.1 OK for: glob@10.4.5 want: ^1.0.0
945 silly placeDep ROOT path-scurry@1.11.1 OK for: glob@10.4.5 want: ^1.11.1
946 silly fetch manifest cross-spawn@^7.0.6
947 silly packumentCache full:https://registry.npmjs.org/cross-spawn cache-miss
948 silly fetch manifest @isaacs/cliui@^8.0.2
949 silly packumentCache full:https://registry.npmjs.org/@isaacs%2fcliui cache-miss
950 silly fetch manifest @pkgjs/parseargs@^0.11.0
951 silly packumentCache full:https://registry.npmjs.org/@pkgjs%2fparseargs cache-miss
952 silly fetch manifest brace-expansion@^2.0.1
953 silly packumentCache full:https://registry.npmjs.org/brace-expansion cache-miss
954 silly fetch manifest lru-cache@^10.2.0
955 silly packumentCache full:https://registry.npmjs.org/lru-cache cache-miss
956 http cache https://registry.npmjs.org/lru-cache 11ms (cache hit)
957 silly packumentCache full:https://registry.npmjs.org/lru-cache set size:400387 disposed:false
958 http fetch GET 200 https://registry.npmjs.org/cross-spawn 34ms (cache miss)
959 silly packumentCache full:https://registry.npmjs.org/cross-spawn set size:undefined disposed:false
960 http fetch GET 200 https://registry.npmjs.org/brace-expansion 43ms (cache miss)
961 silly packumentCache full:https://registry.npmjs.org/brace-expansion set size:undefined disposed:false
962 http fetch GET 200 https://registry.npmjs.org/@pkgjs%2fparseargs 66ms (cache miss)
963 silly packumentCache full:https://registry.npmjs.org/@pkgjs%2fparseargs set size:undefined disposed:false
964 http fetch GET 200 https://registry.npmjs.org/@isaacs%2fcliui 86ms (cache miss)
965 silly packumentCache full:https://registry.npmjs.org/@isaacs%2fcliui set size:undefined disposed:false
966 silly placeDep ROOT cross-spawn@7.0.6 OK for: foreground-child@3.3.1 want: ^7.0.6
967 silly placeDep ROOT signal-exit@4.1.0 OK for: foreground-child@3.3.1 want: ^4.0.1
968 silly fetch manifest which@^2.0.1
969 silly packumentCache full:https://registry.npmjs.org/which cache-miss
970 silly fetch manifest path-key@^3.1.0
971 silly packumentCache full:https://registry.npmjs.org/path-key cache-miss
972 silly fetch manifest shebang-command@^2.0.0
973 silly packumentCache full:https://registry.npmjs.org/shebang-command cache-miss
974 http fetch GET 200 https://registry.npmjs.org/path-key 33ms (cache miss)
975 silly packumentCache full:https://registry.npmjs.org/path-key set size:undefined disposed:false
976 http fetch GET 200 https://registry.npmjs.org/shebang-command 36ms (cache miss)
977 silly packumentCache full:https://registry.npmjs.org/shebang-command set size:undefined disposed:false
978 http fetch GET 200 https://registry.npmjs.org/which 41ms (cache miss)
979 silly packumentCache full:https://registry.npmjs.org/which set size:undefined disposed:false
980 silly placeDep ROOT path-key@3.1.1 OK for: cross-spawn@7.0.6 want: ^3.1.0
981 silly placeDep ROOT shebang-command@2.0.0 OK for: cross-spawn@7.0.6 want: ^2.0.0
982 silly placeDep ROOT which@2.0.2 OK for: cross-spawn@7.0.6 want: ^2.0.1
983 silly fetch manifest shebang-regex@^3.0.0
984 silly packumentCache full:https://registry.npmjs.org/shebang-regex cache-miss
985 silly fetch manifest isexe@^2.0.0
986 silly packumentCache full:https://registry.npmjs.org/isexe cache-miss
987 http fetch GET 200 https://registry.npmjs.org/shebang-regex 31ms (cache miss)
988 silly packumentCache full:https://registry.npmjs.org/shebang-regex set size:undefined disposed:false
989 http fetch GET 200 https://registry.npmjs.org/isexe 46ms (cache miss)
990 silly packumentCache full:https://registry.npmjs.org/isexe set size:undefined disposed:false
991 silly placeDep ROOT pkg-dir@4.2.0 OK for: import-local@3.2.0 want: ^4.2.0
992 silly placeDep ROOT resolve-cwd@3.0.0 OK for: import-local@3.2.0 want: ^3.0.0
993 silly placeDep node_modules/istanbul-lib-instrument semver@7.7.2 OK for: istanbul-lib-instrument@6.0.3 want: ^7.5.4
994 silly placeDep ROOT make-dir@4.0.0 OK for: istanbul-lib-report@3.0.1 want: ^4.0.0
995 silly fetch manifest semver@^7.5.3
996 silly packumentCache full:https://registry.npmjs.org/semver cache-hit
997 silly placeDep ROOT html-escaper@2.0.2 OK for: istanbul-reports@3.2.0 want: ^2.0.0
998 silly placeDep ROOT @isaacs/cliui@8.0.2 OK for: jackspeak@3.4.3 want: ^8.0.2
999 silly placeDep ROOT @pkgjs/parseargs@0.11.0 OK for: jackspeak@3.4.3 want: ^0.11.0
1000 silly fetch manifest string-width@^5.1.2
1001 silly packumentCache full:https://registry.npmjs.org/string-width cache-miss
1002 silly fetch manifest string-width-cjs@npm:string-width@^4.2.0
1003 silly packumentCache full:https://registry.npmjs.org/string-width cache-miss
1004 silly fetch manifest strip-ansi@^7.0.1
1005 silly packumentCache full:https://registry.npmjs.org/strip-ansi cache-miss
1006 silly fetch manifest strip-ansi-cjs@npm:strip-ansi@^6.0.1
1007 silly packumentCache full:https://registry.npmjs.org/strip-ansi cache-miss
1008 silly fetch manifest wrap-ansi@^8.1.0
1009 silly packumentCache full:https://registry.npmjs.org/wrap-ansi cache-miss
1010 silly fetch manifest wrap-ansi-cjs@npm:wrap-ansi@^7.0.0
1011 silly packumentCache full:https://registry.npmjs.org/wrap-ansi cache-miss
1012 http cache https://registry.npmjs.org/strip-ansi 7ms (cache hit)
1013 silly packumentCache full:https://registry.npmjs.org/strip-ansi set size:41855 disposed:false
1014 http cache https://registry.npmjs.org/strip-ansi 8ms (cache hit)
1015 silly packumentCache full:https://registry.npmjs.org/strip-ansi set size:41855 disposed:false
1016 silly packumentCache full:https://registry.npmjs.org/strip-ansi dispose
1017 http fetch GET 200 https://registry.npmjs.org/wrap-ansi 36ms (cache miss)
1018 silly packumentCache full:https://registry.npmjs.org/wrap-ansi set size:undefined disposed:false
1019 http fetch GET 200 https://registry.npmjs.org/wrap-ansi 39ms (cache miss)
1020 silly packumentCache full:https://registry.npmjs.org/wrap-ansi set size:undefined disposed:false
1021 http fetch GET 200 https://registry.npmjs.org/string-width 40ms (cache miss)
1022 silly packumentCache full:https://registry.npmjs.org/string-width set size:undefined disposed:false
1023 http fetch GET 200 https://registry.npmjs.org/string-width 57ms (cache miss)
1024 silly packumentCache full:https://registry.npmjs.org/string-width set size:undefined disposed:false
1025 silly placeDep ROOT string-width@5.1.2 OK for: @isaacs/cliui@8.0.2 want: ^5.1.2
1026 silly placeDep ROOT string-width-cjs@4.2.3 OK for: @isaacs/cliui@8.0.2 want: npm:string-width@^4.2.0
1027 silly placeDep ROOT strip-ansi@7.1.2 OK for: @isaacs/cliui@8.0.2 want: ^7.0.1
1028 silly placeDep ROOT strip-ansi-cjs@6.0.1 OK for: @isaacs/cliui@8.0.2 want: npm:strip-ansi@^6.0.1
1029 silly placeDep ROOT wrap-ansi@8.1.0 OK for: @isaacs/cliui@8.0.2 want: ^8.1.0
1030 silly placeDep ROOT wrap-ansi-cjs@7.0.0 OK for: @isaacs/cliui@8.0.2 want: npm:wrap-ansi@^7.0.0
1031 silly fetch manifest emoji-regex@^9.2.2
1032 silly packumentCache full:https://registry.npmjs.org/emoji-regex cache-miss
1033 silly fetch manifest eastasianwidth@^0.2.0
1034 silly packumentCache full:https://registry.npmjs.org/eastasianwidth cache-miss
1035 silly fetch manifest strip-ansi@^6.0.1
1036 silly packumentCache full:https://registry.npmjs.org/strip-ansi cache-hit
1037 silly fetch manifest emoji-regex@^8.0.0
1038 silly packumentCache full:https://registry.npmjs.org/emoji-regex cache-miss
1039 silly fetch manifest is-fullwidth-code-point@^3.0.0
1040 silly packumentCache full:https://registry.npmjs.org/is-fullwidth-code-point cache-miss
1041 silly fetch manifest ansi-regex@^6.0.1
1042 silly packumentCache full:https://registry.npmjs.org/ansi-regex cache-miss
1043 silly fetch manifest ansi-regex@^5.0.1
1044 silly packumentCache full:https://registry.npmjs.org/ansi-regex cache-miss
1045 silly fetch manifest ansi-styles@^6.1.0
1046 silly packumentCache full:https://registry.npmjs.org/ansi-styles cache-hit
1047 silly fetch manifest string-width@^4.1.0
1048 silly packumentCache full:https://registry.npmjs.org/string-width cache-miss
1049 http cache https://registry.npmjs.org/string-width 2ms (cache hit)
1050 silly packumentCache full:https://registry.npmjs.org/string-width set size:64158 disposed:false
1051 http fetch GET 200 https://registry.npmjs.org/eastasianwidth 34ms (cache miss)
1052 silly packumentCache full:https://registry.npmjs.org/eastasianwidth set size:undefined disposed:false
1053 http fetch GET 200 https://registry.npmjs.org/is-fullwidth-code-point 34ms (cache miss)
1054 silly packumentCache full:https://registry.npmjs.org/is-fullwidth-code-point set size:undefined disposed:false
1055 http fetch GET 200 https://registry.npmjs.org/ansi-regex 35ms (cache miss)
1056 silly packumentCache full:https://registry.npmjs.org/ansi-regex set size:undefined disposed:false
1057 http fetch GET 200 https://registry.npmjs.org/ansi-regex 37ms (cache miss)
1058 silly packumentCache full:https://registry.npmjs.org/ansi-regex set size:undefined disposed:false
1059 http fetch GET 200 https://registry.npmjs.org/emoji-regex 48ms (cache miss)
1060 silly packumentCache full:https://registry.npmjs.org/emoji-regex set size:undefined disposed:false
1061 http fetch GET 200 https://registry.npmjs.org/emoji-regex 50ms (cache miss)
1062 silly packumentCache full:https://registry.npmjs.org/emoji-regex set size:undefined disposed:false
1063 silly placeDep ROOT execa@5.1.1 OK for: jest-changed-files@30.2.0 want: ^5.1.1
1064 silly placeDep ROOT p-limit@3.1.0 OK for: jest-changed-files@30.2.0 want: ^3.1.0
1065 silly fetch manifest onetime@^5.1.2
1066 silly packumentCache full:https://registry.npmjs.org/onetime cache-miss
1067 silly fetch manifest is-stream@^2.0.0
1068 silly packumentCache full:https://registry.npmjs.org/is-stream cache-miss
1069 silly fetch manifest get-stream@^6.0.0
1070 silly packumentCache full:https://registry.npmjs.org/get-stream cache-miss
1071 silly fetch manifest signal-exit@^3.0.3
1072 silly packumentCache full:https://registry.npmjs.org/signal-exit cache-miss
1073 silly fetch manifest npm-run-path@^4.0.1
1074 silly packumentCache full:https://registry.npmjs.org/npm-run-path cache-miss
1075 silly fetch manifest human-signals@^2.1.0
1076 silly packumentCache full:https://registry.npmjs.org/human-signals cache-miss
1077 silly fetch manifest strip-final-newline@^2.0.0
1078 silly packumentCache full:https://registry.npmjs.org/strip-final-newline cache-miss
1079 http cache https://registry.npmjs.org/signal-exit 2ms (cache hit)
1080 silly packumentCache full:https://registry.npmjs.org/signal-exit set size:53489 disposed:false
1081 silly fetch manifest yocto-queue@^0.1.0
1082 silly packumentCache full:https://registry.npmjs.org/yocto-queue cache-miss
1083 http fetch GET 200 https://registry.npmjs.org/get-stream 36ms (cache miss)
1084 silly packumentCache full:https://registry.npmjs.org/get-stream set size:undefined disposed:false
1085 http fetch GET 200 https://registry.npmjs.org/strip-final-newline 45ms (cache miss)
1086 silly packumentCache full:https://registry.npmjs.org/strip-final-newline set size:undefined disposed:false
1087 http fetch GET 200 https://registry.npmjs.org/is-stream 46ms (cache miss)
1088 silly packumentCache full:https://registry.npmjs.org/is-stream set size:undefined disposed:false
1089 http fetch GET 200 https://registry.npmjs.org/human-signals 49ms (cache miss)
1090 silly packumentCache full:https://registry.npmjs.org/human-signals set size:undefined disposed:false
1091 http fetch GET 200 https://registry.npmjs.org/npm-run-path 49ms (cache miss)
1092 silly packumentCache full:https://registry.npmjs.org/npm-run-path set size:undefined disposed:false
1093 http fetch GET 200 https://registry.npmjs.org/yocto-queue 49ms (cache miss)
1094 silly packumentCache full:https://registry.npmjs.org/yocto-queue set size:undefined disposed:false
1095 http fetch GET 200 https://registry.npmjs.org/onetime 58ms (cache miss)
1096 silly packumentCache full:https://registry.npmjs.org/onetime set size:undefined disposed:false
1097 silly placeDep ROOT get-stream@6.0.1 OK for: execa@5.1.1 want: ^6.0.0
1098 silly placeDep ROOT human-signals@2.1.0 OK for: execa@5.1.1 want: ^2.1.0
1099 silly placeDep ROOT is-stream@2.0.1 OK for: execa@5.1.1 want: ^2.0.0
1100 silly placeDep ROOT merge-stream@2.0.0 OK for: execa@5.1.1 want: ^2.0.0
1101 silly placeDep ROOT npm-run-path@4.0.1 OK for: execa@5.1.1 want: ^4.0.1
1102 silly placeDep ROOT onetime@5.1.2 OK for: execa@5.1.1 want: ^5.1.2
1103 silly placeDep node_modules/execa signal-exit@3.0.7 OK for: execa@5.1.1 want: ^3.0.3
1104 silly placeDep ROOT strip-final-newline@2.0.0 OK for: execa@5.1.1 want: ^2.0.0
1105 silly fetch manifest mimic-fn@^2.1.0
1106 silly packumentCache full:https://registry.npmjs.org/mimic-fn cache-miss
1107 http fetch GET 200 https://registry.npmjs.org/mimic-fn 32ms (cache miss)
1108 silly packumentCache full:https://registry.npmjs.org/mimic-fn set size:undefined disposed:false
1109 silly placeDep ROOT yargs@17.7.2 OK for: jest-cli@30.2.0 want: ^17.7.2
1110 silly fetch manifest y18n@^5.0.5
1111 silly packumentCache full:https://registry.npmjs.org/y18n cache-miss
1112 silly fetch manifest cliui@^8.0.1
1113 silly packumentCache full:https://registry.npmjs.org/cliui cache-miss
1114 silly fetch manifest escalade@^3.1.1
1115 silly packumentCache full:https://registry.npmjs.org/escalade cache-miss
1116 silly fetch manifest string-width@^4.2.3
1117 silly packumentCache full:https://registry.npmjs.org/string-width cache-hit
1118 silly fetch manifest yargs-parser@^21.1.1
1119 silly packumentCache full:https://registry.npmjs.org/yargs-parser cache-miss
1120 silly fetch manifest get-caller-file@^2.0.5
1121 silly packumentCache full:https://registry.npmjs.org/get-caller-file cache-miss
1122 silly fetch manifest require-directory@^2.1.1
1123 silly packumentCache full:https://registry.npmjs.org/require-directory cache-miss
1124 http cache https://registry.npmjs.org/escalade 4ms (cache hit)
1125 silly packumentCache full:https://registry.npmjs.org/escalade set size:33724 disposed:false
1126 http fetch GET 200 https://registry.npmjs.org/y18n 35ms (cache miss)
1127 silly packumentCache full:https://registry.npmjs.org/y18n set size:undefined disposed:false
1128 http fetch GET 200 https://registry.npmjs.org/get-caller-file 36ms (cache miss)
1129 silly packumentCache full:https://registry.npmjs.org/get-caller-file set size:undefined disposed:false
1130 http fetch GET 200 https://registry.npmjs.org/yargs-parser 40ms (cache miss)
1131 silly packumentCache full:https://registry.npmjs.org/yargs-parser set size:undefined disposed:false
1132 http fetch GET 200 https://registry.npmjs.org/cliui 69ms (cache miss)
1133 silly packumentCache full:https://registry.npmjs.org/cliui set size:undefined disposed:false
1134 http fetch GET 200 https://registry.npmjs.org/require-directory 652ms (cache miss)
1135 silly packumentCache full:https://registry.npmjs.org/require-directory set size:undefined disposed:false
1136 silly placeDep ROOT @jest/get-type@30.1.0 OK for: jest-config@30.2.0 want: 30.1.0
1137 silly placeDep ROOT @jest/test-sequencer@30.2.0 OK for: jest-config@30.2.0 want: 30.2.0
1138 silly placeDep ROOT babel-jest@30.2.0 OK for: jest-config@30.2.0 want: 30.2.0
1139 silly placeDep ROOT deepmerge@4.3.1 OK for: jest-config@30.2.0 want: ^4.3.1
1140 silly placeDep ROOT jest-circus@30.2.0 OK for: jest-config@30.2.0 want: 30.2.0
1141 silly placeDep ROOT jest-docblock@30.2.0 OK for: jest-config@30.2.0 want: 30.2.0
1142 silly placeDep ROOT jest-environment-node@30.2.0 OK for: jest-config@30.2.0 want: 30.2.0
1143 silly placeDep ROOT parse-json@5.2.0 OK for: jest-config@30.2.0 want: ^5.2.0
1144 silly placeDep ROOT strip-json-comments@3.1.1 OK for: jest-config@30.2.0 want: ^3.1.1
1145 silly fetch manifest @types/babel__core@^7.20.5
1146 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__core cache-miss
1147 silly fetch manifest babel-preset-jest@30.2.0
1148 silly packumentCache full:https://registry.npmjs.org/babel-preset-jest cache-miss
1149 silly fetch manifest @jest/expect@30.2.0
1150 silly packumentCache full:https://registry.npmjs.org/@jest%2fexpect cache-miss
1151 silly fetch manifest co@^4.6.0
1152 silly packumentCache full:https://registry.npmjs.org/co cache-miss
1153 silly fetch manifest dedent@^1.6.0
1154 silly packumentCache full:https://registry.npmjs.org/dedent cache-miss
1155 silly fetch manifest is-generator-fn@^2.1.0
1156 silly packumentCache full:https://registry.npmjs.org/is-generator-fn cache-miss
1157 silly fetch manifest jest-each@30.2.0
1158 silly packumentCache full:https://registry.npmjs.org/jest-each cache-miss
1159 http fetch GET 200 https://registry.npmjs.org/co 42ms (cache miss)
1160 silly packumentCache full:https://registry.npmjs.org/co set size:undefined disposed:false
1161 silly fetch manifest pure-rand@^7.0.0
1162 silly packumentCache full:https://registry.npmjs.org/pure-rand cache-miss
1163 http fetch GET 200 https://registry.npmjs.org/dedent 44ms (cache miss)
1164 silly packumentCache full:https://registry.npmjs.org/dedent set size:undefined disposed:false
1165 silly fetch manifest detect-newline@^3.1.0
1166 silly packumentCache full:https://registry.npmjs.org/detect-newline cache-miss
1167 http fetch GET 200 https://registry.npmjs.org/is-generator-fn 49ms (cache miss)
1168 silly packumentCache full:https://registry.npmjs.org/is-generator-fn set size:undefined disposed:false
1169 silly fetch manifest error-ex@^1.3.1
1170 silly packumentCache full:https://registry.npmjs.org/error-ex cache-miss
1171 http fetch GET 200 https://registry.npmjs.org/babel-preset-jest 53ms (cache miss)
1172 silly packumentCache full:https://registry.npmjs.org/babel-preset-jest set size:undefined disposed:false
1173 silly fetch manifest lines-and-columns@^1.1.6
1174 silly packumentCache full:https://registry.npmjs.org/lines-and-columns cache-miss
1175 http fetch GET 200 https://registry.npmjs.org/@jest%2fexpect 58ms (cache miss)
1176 silly packumentCache full:https://registry.npmjs.org/@jest%2fexpect set size:undefined disposed:false
1177 silly fetch manifest json-parse-even-better-errors@^2.3.0
1178 silly packumentCache full:https://registry.npmjs.org/json-parse-even-better-errors cache-miss
1179 http fetch GET 200 https://registry.npmjs.org/jest-each 73ms (cache miss)
1180 silly packumentCache full:https://registry.npmjs.org/jest-each set size:undefined disposed:false
1181 http fetch GET 200 https://registry.npmjs.org/pure-rand 37ms (cache miss)
1182 silly packumentCache full:https://registry.npmjs.org/pure-rand set size:undefined disposed:false
1183 http fetch GET 200 https://registry.npmjs.org/error-ex 35ms (cache miss)
1184 silly packumentCache full:https://registry.npmjs.org/error-ex set size:undefined disposed:false
1185 http fetch GET 200 https://registry.npmjs.org/detect-newline 40ms (cache miss)
1186 silly packumentCache full:https://registry.npmjs.org/detect-newline set size:undefined disposed:false
1187 http fetch GET 200 https://registry.npmjs.org/json-parse-even-better-errors 32ms (cache miss)
1188 silly packumentCache full:https://registry.npmjs.org/json-parse-even-better-errors set size:undefined disposed:false
1189 http fetch GET 200 https://registry.npmjs.org/lines-and-columns 45ms (cache miss)
1190 silly packumentCache full:https://registry.npmjs.org/lines-and-columns set size:undefined disposed:false
1191 http fetch GET 200 https://registry.npmjs.org/@types%2fbabel__core 131ms (cache miss)
1192 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__core set size:undefined disposed:false
1193 silly fetch manifest @babel/core@^7.11.0 || ^8.0.0-0
1194 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore cache-hit
1195 silly placeDep ROOT @types/babel__core@7.20.5 OK for: babel-jest@30.2.0 want: ^7.20.5
1196 silly placeDep ROOT babel-preset-jest@30.2.0 OK for: babel-jest@30.2.0 want: 30.2.0
1197 silly fetch manifest @types/babel__template@*
1198 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__template cache-miss
1199 silly fetch manifest @types/babel__traverse@*
1200 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__traverse cache-miss
1201 silly fetch manifest @types/babel__generator@*
1202 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__generator cache-miss
1203 silly fetch manifest babel-plugin-jest-hoist@30.2.0
1204 silly packumentCache full:https://registry.npmjs.org/babel-plugin-jest-hoist cache-miss
1205 http fetch GET 200 https://registry.npmjs.org/@types%2fbabel__generator 46ms (cache miss)
1206 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__generator set size:undefined disposed:false
1207 http fetch GET 200 https://registry.npmjs.org/@types%2fbabel__traverse 47ms (cache miss)
1208 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__traverse set size:undefined disposed:false
1209 http fetch GET 200 https://registry.npmjs.org/@types%2fbabel__template 48ms (cache miss)
1210 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__template set size:undefined disposed:false
1211 http fetch GET 200 https://registry.npmjs.org/babel-plugin-jest-hoist 75ms (cache miss)
1212 silly packumentCache full:https://registry.npmjs.org/babel-plugin-jest-hoist set size:undefined disposed:false
1213 silly placeDep ROOT @types/babel__generator@7.27.0 OK for: @types/babel__core@7.20.5 want: *
1214 silly placeDep ROOT @types/babel__template@7.4.4 OK for: @types/babel__core@7.20.5 want: *
1215 silly placeDep ROOT @types/babel__traverse@7.28.0 OK for: @types/babel__core@7.20.5 want: *
1216 silly fetch manifest @babel/core@^7.11.0 || ^8.0.0-beta.1
1217 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore cache-hit
1218 silly placeDep ROOT babel-plugin-jest-hoist@30.2.0 OK for: babel-preset-jest@30.2.0 want: 30.2.0
1219 silly placeDep ROOT babel-preset-current-node-syntax@1.2.0 OK for: babel-preset-jest@30.2.0 want: ^1.2.0
1220 silly fetch manifest @babel/plugin-syntax-async-generators@^7.8.4
1221 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-async-generators cache-miss
1222 silly fetch manifest @babel/plugin-syntax-bigint@^7.8.3
1223 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-bigint cache-miss
1224 silly fetch manifest @babel/plugin-syntax-class-properties@^7.12.13
1225 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-class-properties cache-miss
1226 silly fetch manifest @babel/plugin-syntax-class-static-block@^7.14.5
1227 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-class-static-block cache-miss
1228 silly fetch manifest @babel/plugin-syntax-import-attributes@^7.24.7
1229 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-import-attributes cache-miss
1230 silly fetch manifest @babel/plugin-syntax-import-meta@^7.10.4
1231 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-import-meta cache-miss
1232 silly fetch manifest @babel/plugin-syntax-json-strings@^7.8.3
1233 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-json-strings cache-miss
1234 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-async-generators 49ms (cache miss)
1235 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-async-generators set size:undefined disposed:false
1236 silly fetch manifest @babel/plugin-syntax-logical-assignment-operators@^7.10.4
1237 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-logical-assignment-operators cache-miss
1238 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-class-static-block 53ms (cache miss)
1239 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-class-static-block set size:undefined disposed:false
1240 silly fetch manifest @babel/plugin-syntax-nullish-coalescing-operator@^7.8.3
1241 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-nullish-coalescing-operator cache-miss
1242 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-import-attributes 64ms (cache miss)
1243 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-import-attributes set size:undefined disposed:false
1244 silly fetch manifest @babel/plugin-syntax-numeric-separator@^7.10.4
1245 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-numeric-separator cache-miss
1246 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-bigint 66ms (cache miss)
1247 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-bigint set size:undefined disposed:false
1248 silly fetch manifest @babel/plugin-syntax-object-rest-spread@^7.8.3
1249 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-object-rest-spread cache-miss
1250 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-json-strings 66ms (cache miss)
1251 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-json-strings set size:undefined disposed:false
1252 silly fetch manifest @babel/plugin-syntax-optional-catch-binding@^7.8.3
1253 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-optional-catch-binding cache-miss
1254 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-class-properties 70ms (cache miss)
1255 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-class-properties set size:undefined disposed:false
1256 silly fetch manifest @babel/plugin-syntax-optional-chaining@^7.8.3
1257 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-optional-chaining cache-miss
1258 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-import-meta 75ms (cache miss)
1259 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-import-meta set size:undefined disposed:false
1260 silly fetch manifest @babel/plugin-syntax-private-property-in-object@^7.14.5
1261 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-private-property-in-object cache-miss
1262 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-logical-assignment-operators 44ms (cache miss)
1263 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-logical-assignment-operators set size:undefined disposed:false
1264 silly fetch manifest @babel/plugin-syntax-top-level-await@^7.14.5
1265 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-top-level-await cache-miss
1266 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-nullish-coalescing-operator 46ms (cache miss)
1267 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-nullish-coalescing-operator set size:undefined disposed:false
1268 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-numeric-separator 52ms (cache miss)
1269 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-numeric-separator set size:undefined disposed:false
1270 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-private-property-in-object 47ms (cache miss)
1271 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-private-property-in-object set size:undefined disposed:false
1272 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-object-rest-spread 57ms (cache miss)
1273 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-object-rest-spread set size:undefined disposed:false
1274 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-optional-catch-binding 57ms (cache miss)
1275 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-optional-catch-binding set size:undefined disposed:false
1276 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-top-level-await 38ms (cache miss)
1277 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-top-level-await set size:undefined disposed:false
1278 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-optional-chaining 70ms (cache miss)
1279 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-optional-chaining set size:undefined disposed:false
1280 silly fetch manifest @babel/core@^7.0.0 || ^8.0.0-0
1281 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore cache-hit
1282 silly placeDep ROOT @babel/plugin-syntax-async-generators@7.8.4 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.8.4
1283 silly placeDep ROOT @babel/plugin-syntax-bigint@7.8.3 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.8.3
1284 silly placeDep ROOT @babel/plugin-syntax-class-properties@7.12.13 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.12.13
1285 silly placeDep ROOT @babel/plugin-syntax-class-static-block@7.14.5 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.14.5
1286 silly placeDep ROOT @babel/plugin-syntax-import-attributes@7.27.1 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.24.7
1287 silly placeDep ROOT @babel/plugin-syntax-import-meta@7.10.4 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.10.4
1288 silly placeDep ROOT @babel/plugin-syntax-json-strings@7.8.3 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.8.3
1289 silly placeDep ROOT @babel/plugin-syntax-logical-assignment-operators@7.10.4 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.10.4
1290 silly placeDep ROOT @babel/plugin-syntax-nullish-coalescing-operator@7.8.3 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.8.3
1291 silly placeDep ROOT @babel/plugin-syntax-numeric-separator@7.10.4 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.10.4
1292 silly placeDep ROOT @babel/plugin-syntax-object-rest-spread@7.8.3 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.8.3
1293 silly placeDep ROOT @babel/plugin-syntax-optional-catch-binding@7.8.3 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.8.3
1294 silly placeDep ROOT @babel/plugin-syntax-optional-chaining@7.8.3 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.8.3
1295 silly placeDep ROOT @babel/plugin-syntax-private-property-in-object@7.14.5 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.14.5
1296 silly placeDep ROOT @babel/plugin-syntax-top-level-await@7.14.5 OK for: babel-preset-current-node-syntax@1.2.0 want: ^7.14.5
1297 silly fetch manifest babel-plugin-macros@^3.1.0
1298 silly packumentCache full:https://registry.npmjs.org/babel-plugin-macros cache-miss
1299 http fetch GET 200 https://registry.npmjs.org/babel-plugin-macros 746ms (cache miss)
1300 silly packumentCache full:https://registry.npmjs.org/babel-plugin-macros set size:undefined disposed:false
1301 silly placeDep ROOT @jest/environment@30.2.0 OK for: jest-circus@30.2.0 want: 30.2.0
1302 silly placeDep ROOT @jest/expect@30.2.0 OK for: jest-circus@30.2.0 want: 30.2.0
1303 silly placeDep ROOT co@4.6.0 OK for: jest-circus@30.2.0 want: ^4.6.0
1304 silly placeDep ROOT dedent@1.7.0 OK for: jest-circus@30.2.0 want: ^1.6.0
1305 silly placeDep ROOT is-generator-fn@2.1.0 OK for: jest-circus@30.2.0 want: ^2.1.0
1306 silly placeDep ROOT jest-each@30.2.0 OK for: jest-circus@30.2.0 want: 30.2.0
1307 silly placeDep ROOT jest-matcher-utils@30.2.0 OK for: jest-circus@30.2.0 want: 30.2.0
1308 silly placeDep ROOT pure-rand@7.0.1 OK for: jest-circus@30.2.0 want: ^7.0.0
1309 silly placeDep ROOT stack-utils@2.0.6 OK for: jest-circus@30.2.0 want: ^2.0.6
1310 silly fetch manifest escape-string-regexp@^2.0.0
1311 silly packumentCache full:https://registry.npmjs.org/escape-string-regexp cache-miss
1312 http fetch GET 200 https://registry.npmjs.org/escape-string-regexp 32ms (cache miss)
1313 silly packumentCache full:https://registry.npmjs.org/escape-string-regexp set size:undefined disposed:false
1314 silly placeDep ROOT @jest/fake-timers@30.2.0 OK for: @jest/environment@30.2.0 want: 30.2.0
1315 silly placeDep ROOT jest-mock@30.2.0 OK for: @jest/environment@30.2.0 want: 30.2.0
1316 silly fetch manifest @sinonjs/fake-timers@^13.0.0
1317 silly packumentCache full:https://registry.npmjs.org/@sinonjs%2ffake-timers cache-miss
1318 http fetch GET 200 https://registry.npmjs.org/@sinonjs%2ffake-timers 59ms (cache miss)
1319 silly packumentCache full:https://registry.npmjs.org/@sinonjs%2ffake-timers set size:undefined disposed:false
1320 silly placeDep ROOT expect@30.2.0 OK for: @jest/expect@30.2.0 want: 30.2.0
1321 silly placeDep ROOT @sinonjs/fake-timers@13.0.5 OK for: @jest/fake-timers@30.2.0 want: ^13.0.0
1322 silly fetch manifest @sinonjs/commons@^3.0.1
1323 silly packumentCache full:https://registry.npmjs.org/@sinonjs%2fcommons cache-miss
1324 http fetch GET 200 https://registry.npmjs.org/@sinonjs%2fcommons 74ms (cache miss)
1325 silly packumentCache full:https://registry.npmjs.org/@sinonjs%2fcommons set size:undefined disposed:false
1326 silly placeDep ROOT @sinonjs/commons@3.0.1 OK for: @sinonjs/fake-timers@13.0.5 want: ^3.0.1
1327 silly fetch manifest type-detect@4.0.8
1328 silly packumentCache full:https://registry.npmjs.org/type-detect cache-miss
1329 http fetch GET 200 https://registry.npmjs.org/type-detect 50ms (cache miss)
1330 silly packumentCache full:https://registry.npmjs.org/type-detect set size:undefined disposed:false
1331 silly placeDep ROOT type-detect@4.0.8 OK for: @sinonjs/commons@3.0.1 want: 4.0.8
1332 silly placeDep ROOT @jest/expect-utils@30.2.0 OK for: expect@30.2.0 want: 30.2.0
1333 silly placeDep ROOT detect-newline@3.1.0 OK for: jest-docblock@30.2.0 want: ^3.1.0
1334 silly placeDep ROOT anymatch@3.1.3 OK for: jest-haste-map@30.2.0 want: ^3.1.3
1335 silly placeDep ROOT fb-watchman@2.0.2 OK for: jest-haste-map@30.2.0 want: ^2.0.2
1336 silly placeDep ROOT fsevents@2.3.3 OK for: jest-haste-map@30.2.0 want: ^2.3.3
1337 silly placeDep ROOT walker@1.0.8 OK for: jest-haste-map@30.2.0 want: ^1.0.8
1338 silly fetch manifest picomatch@^2.0.4
1339 silly packumentCache full:https://registry.npmjs.org/picomatch cache-miss
1340 silly fetch manifest normalize-path@^3.0.0
1341 silly packumentCache full:https://registry.npmjs.org/normalize-path cache-miss
1342 silly fetch manifest bser@2.1.1
1343 silly packumentCache full:https://registry.npmjs.org/bser cache-miss
1344 silly fetch manifest makeerror@1.0.12
1345 silly packumentCache full:https://registry.npmjs.org/makeerror cache-miss
1346 http cache https://registry.npmjs.org/picomatch 2ms (cache hit)
1347 silly packumentCache full:https://registry.npmjs.org/picomatch set size:118818 disposed:false
1348 http fetch GET 200 https://registry.npmjs.org/normalize-path 33ms (cache miss)
1349 silly packumentCache full:https://registry.npmjs.org/normalize-path set size:undefined disposed:false
1350 http fetch GET 200 https://registry.npmjs.org/makeerror 34ms (cache miss)
1351 silly packumentCache full:https://registry.npmjs.org/makeerror set size:undefined disposed:false
1352 http fetch GET 200 https://registry.npmjs.org/bser 64ms (cache miss)
1353 silly packumentCache full:https://registry.npmjs.org/bser set size:undefined disposed:false
1354 silly placeDep ROOT normalize-path@3.0.0 OK for: anymatch@3.1.3 want: ^3.0.0
1355 silly placeDep ROOT picomatch@2.3.1 OK for: anymatch@3.1.3 want: ^2.0.4
1356 silly placeDep ROOT bser@2.1.1 OK for: fb-watchman@2.0.2 want: 2.1.1
1357 silly fetch manifest node-int64@^0.4.0
1358 silly packumentCache full:https://registry.npmjs.org/node-int64 cache-miss
1359 http fetch GET 200 https://registry.npmjs.org/node-int64 30ms (cache miss)
1360 silly packumentCache full:https://registry.npmjs.org/node-int64 set size:undefined disposed:false
1361 silly placeDep ROOT node-int64@0.4.0 OK for: bser@2.1.1 want: ^0.4.0
1362 silly placeDep ROOT jest-diff@30.2.0 OK for: jest-matcher-utils@30.2.0 want: 30.2.0
1363 silly fetch manifest @jest/diff-sequences@30.0.1
1364 silly packumentCache full:https://registry.npmjs.org/@jest%2fdiff-sequences cache-miss
1365 http fetch GET 200 https://registry.npmjs.org/@jest%2fdiff-sequences 60ms (cache miss)
1366 silly packumentCache full:https://registry.npmjs.org/@jest%2fdiff-sequences set size:undefined disposed:false
1367 silly placeDep ROOT @jest/diff-sequences@30.0.1 OK for: jest-diff@30.2.0 want: 30.0.1
1368 silly placeDep ROOT @types/stack-utils@2.0.3 OK for: jest-message-util@30.2.0 want: ^2.0.3
1369 silly fetch manifest jest-resolve@*
1370 silly packumentCache full:https://registry.npmjs.org/jest-resolve cache-miss
1371 http cache https://registry.npmjs.org/jest-resolve 3ms (cache hit)
1372 silly packumentCache full:https://registry.npmjs.org/jest-resolve set size:654149 disposed:false
1373 silly placeDep ROOT jest-pnp-resolver@1.2.3 OK for: jest-resolve@30.2.0 want: ^1.2.3
1374 silly placeDep ROOT unrs-resolver@1.11.1 OK for: jest-resolve@30.2.0 want: ^1.7.11
1375 silly fetch manifest napi-postinstall@^0.3.0
1376 silly packumentCache full:https://registry.npmjs.org/napi-postinstall cache-miss
1377 silly fetch manifest @unrs/resolver-binding-win32-x64-msvc@1.11.1
1378 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-win32-x64-msvc cache-miss
1379 silly fetch manifest @unrs/resolver-binding-win32-arm64-msvc@1.11.1
1380 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-win32-arm64-msvc cache-miss
1381 silly fetch manifest @unrs/resolver-binding-win32-ia32-msvc@1.11.1
1382 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-win32-ia32-msvc cache-miss
1383 silly fetch manifest @unrs/resolver-binding-linux-x64-gnu@1.11.1
1384 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-x64-gnu cache-miss
1385 silly fetch manifest @unrs/resolver-binding-linux-x64-musl@1.11.1
1386 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-x64-musl cache-miss
1387 silly fetch manifest @unrs/resolver-binding-freebsd-x64@1.11.1
1388 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-freebsd-x64 cache-miss
1389 http fetch GET 200 https://registry.npmjs.org/napi-postinstall 34ms (cache miss)
1390 silly packumentCache full:https://registry.npmjs.org/napi-postinstall set size:undefined disposed:false
1391 silly fetch manifest @unrs/resolver-binding-android-arm64@1.11.1
1392 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-android-arm64 cache-miss
1393 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-x64-musl 51ms (cache miss)
1394 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-x64-musl set size:undefined disposed:false
1395 silly fetch manifest @unrs/resolver-binding-linux-arm64-gnu@1.11.1
1396 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm64-gnu cache-miss
1397 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-win32-x64-msvc 56ms (cache miss)
1398 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-win32-x64-msvc set size:undefined disposed:false
1399 silly fetch manifest @unrs/resolver-binding-linux-arm64-musl@1.11.1
1400 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm64-musl cache-miss
1401 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-win32-arm64-msvc 57ms (cache miss)
1402 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-win32-arm64-msvc set size:undefined disposed:false
1403 silly fetch manifest @unrs/resolver-binding-android-arm-eabi@1.11.1
1404 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-android-arm-eabi cache-miss
1405 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-freebsd-x64 60ms (cache miss)
1406 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-freebsd-x64 set size:undefined disposed:false
1407 silly fetch manifest @unrs/resolver-binding-linux-arm-gnueabihf@1.11.1
1408 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm-gnueabihf cache-miss
1409 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-win32-ia32-msvc 67ms (cache miss)
1410 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-win32-ia32-msvc set size:undefined disposed:false
1411 silly fetch manifest @unrs/resolver-binding-linux-arm-musleabihf@1.11.1
1412 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm-musleabihf cache-miss
1413 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-x64-gnu 93ms (cache miss)
1414 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-x64-gnu set size:undefined disposed:false
1415 silly fetch manifest @unrs/resolver-binding-linux-ppc64-gnu@1.11.1
1416 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-ppc64-gnu cache-miss
1417 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm64-gnu 44ms (cache miss)
1418 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm64-gnu set size:undefined disposed:false
1419 silly fetch manifest @unrs/resolver-binding-linux-riscv64-gnu@1.11.1
1420 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-riscv64-gnu cache-miss
1421 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-android-arm64 72ms (cache miss)
1422 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-android-arm64 set size:undefined disposed:false
1423 silly fetch manifest @unrs/resolver-binding-linux-riscv64-musl@1.11.1
1424 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-riscv64-musl cache-miss
1425 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm64-musl 62ms (cache miss)
1426 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm64-musl set size:undefined disposed:false
1427 silly fetch manifest @unrs/resolver-binding-linux-s390x-gnu@1.11.1
1428 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-s390x-gnu cache-miss
1429 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm-gnueabihf 61ms (cache miss)
1430 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm-gnueabihf set size:undefined disposed:false
1431 silly fetch manifest @unrs/resolver-binding-darwin-x64@1.11.1
1432 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-darwin-x64 cache-miss
1433 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm-musleabihf 58ms (cache miss)
1434 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-arm-musleabihf set size:undefined disposed:false
1435 silly fetch manifest @unrs/resolver-binding-darwin-arm64@1.11.1
1436 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-darwin-arm64 cache-miss
1437 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-android-arm-eabi 71ms (cache miss)
1438 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-android-arm-eabi set size:undefined disposed:false
1439 silly fetch manifest @unrs/resolver-binding-wasm32-wasi@1.11.1
1440 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-wasm32-wasi cache-miss
1441 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-riscv64-gnu 52ms (cache miss)
1442 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-riscv64-gnu set size:undefined disposed:false
1443 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-riscv64-musl 63ms (cache miss)
1444 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-riscv64-musl set size:undefined disposed:false
1445 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-darwin-x64 56ms (cache miss)
1446 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-darwin-x64 set size:undefined disposed:false
1447 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-wasm32-wasi 55ms (cache miss)
1448 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-wasm32-wasi set size:undefined disposed:false
1449 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-ppc64-gnu 107ms (cache miss)
1450 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-ppc64-gnu set size:undefined disposed:false
1451 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-linux-s390x-gnu 81ms (cache miss)
1452 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-linux-s390x-gnu set size:undefined disposed:false
1453 http fetch GET 200 https://registry.npmjs.org/@unrs%2fresolver-binding-darwin-arm64 79ms (cache miss)
1454 silly packumentCache full:https://registry.npmjs.org/@unrs%2fresolver-binding-darwin-arm64 set size:undefined disposed:false
1455 silly placeDep ROOT emittery@0.13.1 OK for: jest-runner@30.2.0 want: ^0.13.1
1456 silly placeDep ROOT jest-leak-detector@30.2.0 OK for: jest-runner@30.2.0 want: 30.2.0
1457 silly placeDep ROOT source-map-support@0.5.13 OK for: jest-runner@30.2.0 want: 0.5.13
1458 silly fetch manifest buffer-from@^1.0.0
1459 silly packumentCache full:https://registry.npmjs.org/buffer-from cache-miss
1460 silly fetch manifest source-map@^0.6.0
1461 silly packumentCache full:https://registry.npmjs.org/source-map cache-miss
1462 http fetch GET 200 https://registry.npmjs.org/buffer-from 34ms (cache miss)
1463 silly packumentCache full:https://registry.npmjs.org/buffer-from set size:undefined disposed:false
1464 http fetch GET 200 https://registry.npmjs.org/source-map 47ms (cache miss)
1465 silly packumentCache full:https://registry.npmjs.org/source-map set size:undefined disposed:false
1466 silly placeDep ROOT @jest/globals@30.2.0 OK for: jest-runtime@30.2.0 want: 30.2.0
1467 silly placeDep ROOT @jest/source-map@30.0.1 OK for: jest-runtime@30.2.0 want: 30.0.1
1468 silly placeDep ROOT cjs-module-lexer@2.1.0 OK for: jest-runtime@30.2.0 want: ^2.1.0
1469 silly placeDep ROOT strip-bom@4.0.0 OK for: jest-runtime@30.2.0 want: ^4.0.0
1470 silly fetch manifest callsites@^3.1.0
1471 silly packumentCache full:https://registry.npmjs.org/callsites cache-miss
1472 http fetch GET 200 https://registry.npmjs.org/callsites 210ms (cache miss)
1473 silly packumentCache full:https://registry.npmjs.org/callsites set size:undefined disposed:false
1474 silly placeDep ROOT callsites@3.1.0 OK for: @jest/source-map@30.0.1 want: ^3.1.0
1475 silly placeDep ROOT @babel/plugin-syntax-jsx@7.27.1 OK for: jest-snapshot@30.2.0 want: ^7.27.1
1476 silly placeDep ROOT @babel/plugin-syntax-typescript@7.27.1 OK for: jest-snapshot@30.2.0 want: ^7.27.1
1477 silly placeDep ROOT @jest/snapshot-utils@30.2.0 OK for: jest-snapshot@30.2.0 want: 30.2.0
1478 silly placeDep node_modules/jest-snapshot semver@7.7.2 OK for: jest-snapshot@30.2.0 want: ^7.7.2
1479 silly placeDep ROOT synckit@0.11.11 OK for: jest-snapshot@30.2.0 want: ^0.11.8
1480 silly fetch manifest natural-compare@^1.4.0
1481 silly packumentCache full:https://registry.npmjs.org/natural-compare cache-miss
1482 silly fetch manifest @pkgr/core@^0.2.9
1483 silly packumentCache full:https://registry.npmjs.org/@pkgr%2fcore cache-miss
1484 http fetch GET 200 https://registry.npmjs.org/natural-compare 31ms (cache miss)
1485 silly packumentCache full:https://registry.npmjs.org/natural-compare set size:undefined disposed:false
1486 http fetch GET 200 https://registry.npmjs.org/@pkgr%2fcore 93ms (cache miss)
1487 silly packumentCache full:https://registry.npmjs.org/@pkgr%2fcore set size:undefined disposed:false
1488 silly placeDep ROOT natural-compare@1.4.0 OK for: @jest/snapshot-utils@30.2.0 want: ^1.4.0
1489 silly placeDep node_modules/jest-util picomatch@4.0.3 OK for: jest-util@30.2.0 want: ^4.0.2
1490 silly placeDep node_modules/jest-validate camelcase@6.3.0 OK for: jest-validate@30.2.0 want: ^6.3.0
1491 silly placeDep ROOT leven@3.1.0 OK for: jest-validate@30.2.0 want: ^3.1.0
1492 silly placeDep ROOT @ungap/structured-clone@1.3.0 OK for: jest-worker@30.2.0 want: ^1.3.0
1493 silly placeDep node_modules/jest-worker supports-color@8.1.1 OK for: jest-worker@30.2.0 want: ^8.1.1
1494 silly placeDep ROOT argparse@1.0.10 OK for: js-yaml@3.14.1 want: ^1.0.7
1495 silly placeDep ROOT esprima@4.0.1 OK for: js-yaml@3.14.1 want: ^4.0.0
1496 silly fetch manifest sprintf-js@~1.0.2
1497 silly packumentCache full:https://registry.npmjs.org/sprintf-js cache-miss
1498 http fetch GET 200 https://registry.npmjs.org/sprintf-js 39ms (cache miss)
1499 silly packumentCache full:https://registry.npmjs.org/sprintf-js set size:undefined disposed:false
1500 silly placeDep ROOT sprintf-js@1.0.3 OK for: argparse@1.0.10 want: ~1.0.2
1501 silly placeDep ROOT p-locate@4.1.0 OK for: locate-path@5.0.0 want: ^4.1.0
1502 silly fetch manifest p-limit@^2.2.0
1503 silly packumentCache full:https://registry.npmjs.org/p-limit cache-miss
1504 http cache https://registry.npmjs.org/p-limit 1ms (cache hit)
1505 silly packumentCache full:https://registry.npmjs.org/p-limit set size:56611 disposed:false
1506 silly placeDep ROOT yallist@3.1.1 OK for: lru-cache@5.1.1 want: ^3.0.2
1507 silly placeDep node_modules/make-dir semver@7.7.2 OK for: make-dir@4.0.0 want: ^7.5.3
1508 silly placeDep ROOT braces@3.0.3 OK for: micromatch@4.0.8 want: ^3.0.3
1509 silly fetch manifest fill-range@^7.1.1
1510 silly packumentCache full:https://registry.npmjs.org/fill-range cache-miss
1511 http fetch GET 200 https://registry.npmjs.org/fill-range 32ms (cache miss)
1512 silly packumentCache full:https://registry.npmjs.org/fill-range set size:undefined disposed:false
1513 silly placeDep ROOT fill-range@7.1.1 OK for: braces@3.0.3 want: ^7.1.1
1514 silly fetch manifest to-regex-range@^5.0.1
1515 silly packumentCache full:https://registry.npmjs.org/to-regex-range cache-miss
1516 http fetch GET 200 https://registry.npmjs.org/to-regex-range 38ms (cache miss)
1517 silly packumentCache full:https://registry.npmjs.org/to-regex-range set size:undefined disposed:false
1518 silly placeDep ROOT to-regex-range@5.0.1 OK for: fill-range@7.1.1 want: ^5.0.1
1519 silly fetch manifest is-number@^7.0.0
1520 silly packumentCache full:https://registry.npmjs.org/is-number cache-miss
1521 http fetch GET 200 https://registry.npmjs.org/is-number 41ms (cache miss)
1522 silly packumentCache full:https://registry.npmjs.org/is-number set size:undefined disposed:false
1523 silly placeDep ROOT brace-expansion@2.0.2 OK for: minimatch@9.0.5 want: ^2.0.1
1524 silly fetch manifest balanced-match@^1.0.0
1525 silly packumentCache full:https://registry.npmjs.org/balanced-match cache-miss
1526 http fetch GET 200 https://registry.npmjs.org/balanced-match 42ms (cache miss)
1527 silly packumentCache full:https://registry.npmjs.org/balanced-match set size:undefined disposed:false
1528 silly placeDep ROOT balanced-match@1.0.2 OK for: brace-expansion@2.0.2 want: ^1.0.0
1529 silly placeDep ROOT mimic-fn@2.1.0 OK for: onetime@5.1.2 want: ^2.1.0
1530 silly placeDep ROOT yocto-queue@0.1.0 OK for: p-limit@3.1.0 want: ^0.1.0
1531 silly placeDep node_modules/p-locate p-limit@2.3.0 OK for: p-locate@4.1.0 want: ^2.2.0
1532 silly fetch manifest p-try@^2.0.0
1533 silly packumentCache full:https://registry.npmjs.org/p-try cache-miss
1534 http fetch GET 200 https://registry.npmjs.org/p-try 32ms (cache miss)
1535 silly packumentCache full:https://registry.npmjs.org/p-try set size:undefined disposed:false
1536 silly placeDep ROOT error-ex@1.3.4 OK for: parse-json@5.2.0 want: ^1.3.1
1537 silly placeDep ROOT json-parse-even-better-errors@2.3.1 OK for: parse-json@5.2.0 want: ^2.3.0
1538 silly placeDep ROOT lines-and-columns@1.2.4 OK for: parse-json@5.2.0 want: ^1.1.6
1539 silly fetch manifest is-arrayish@^0.2.1
1540 silly packumentCache full:https://registry.npmjs.org/is-arrayish cache-miss
1541 http fetch GET 200 https://registry.npmjs.org/is-arrayish 49ms (cache miss)
1542 silly packumentCache full:https://registry.npmjs.org/is-arrayish set size:undefined disposed:false
1543 silly placeDep ROOT is-arrayish@0.2.1 OK for: error-ex@1.3.4 want: ^0.2.1
1544 silly placeDep node_modules/path-scurry lru-cache@10.4.3 OK for: path-scurry@1.11.1 want: ^10.2.0
1545 silly placeDep node_modules/pretty-format ansi-styles@5.2.0 OK for: pretty-format@30.2.0 want: ^5.2.0
1546 silly placeDep ROOT react-is@18.3.1 OK for: pretty-format@30.2.0 want: ^18.3.1
1547 silly placeDep ROOT shebang-regex@3.0.0 OK for: shebang-command@2.0.0 want: ^3.0.0
1548 silly placeDep ROOT buffer-from@1.1.2 OK for: source-map-support@0.5.13 want: ^1.0.0
1549 silly placeDep ROOT source-map@0.6.1 OK for: source-map-support@0.5.13 want: ^0.6.0
1550 silly placeDep ROOT escape-string-regexp@2.0.0 OK for: stack-utils@2.0.6 want: ^2.0.0
1551 silly placeDep ROOT char-regex@1.0.2 OK for: string-length@4.0.2 want: ^1.0.2
1552 silly placeDep node_modules/string-length strip-ansi@6.0.1 OK for: string-length@4.0.2 want: ^6.0.0
1553 silly placeDep ROOT eastasianwidth@0.2.0 OK for: string-width@5.1.2 want: ^0.2.0
1554 silly placeDep ROOT emoji-regex@9.2.2 OK for: string-width@5.1.2 want: ^9.2.2
1555 silly placeDep node_modules/string-width-cjs emoji-regex@8.0.0 OK for: string-width@4.2.3 want: ^8.0.0
1556 silly placeDep ROOT is-fullwidth-code-point@3.0.0 OK for: string-width@4.2.3 want: ^3.0.0
1557 silly placeDep node_modules/string-width-cjs strip-ansi@6.0.1 OK for: string-width@4.2.3 want: ^6.0.1
1558 silly placeDep ROOT ansi-regex@6.2.2 OK for: strip-ansi@7.1.2 want: ^6.0.1
1559 silly placeDep node_modules/strip-ansi-cjs ansi-regex@5.0.1 OK for: strip-ansi@6.0.1 want: ^5.0.1
1560 silly placeDep ROOT has-flag@4.0.0 OK for: supports-color@7.2.0 want: ^4.0.0
1561 silly placeDep ROOT @pkgr/core@0.2.9 OK for: synckit@0.11.11 want: ^0.2.9
1562 silly placeDep node_modules/test-exclude glob@7.2.3 OK for: test-exclude@6.0.0 want: ^7.1.4
1563 silly placeDep node_modules/test-exclude minimatch@3.1.2 OK for: test-exclude@6.0.0 want: ^3.0.4
1564 silly fetch manifest once@^1.3.0
1565 silly packumentCache full:https://registry.npmjs.org/once cache-miss
1566 silly fetch manifest inflight@^1.0.4
1567 silly packumentCache full:https://registry.npmjs.org/inflight cache-miss
1568 silly fetch manifest inherits@2
1569 silly packumentCache full:https://registry.npmjs.org/inherits cache-miss
1570 silly fetch manifest minimatch@^3.1.1
1571 silly packumentCache full:https://registry.npmjs.org/minimatch cache-hit
1572 silly fetch manifest fs.realpath@^1.0.0
1573 silly packumentCache full:https://registry.npmjs.org/fs.realpath cache-miss
1574 silly fetch manifest path-is-absolute@^1.0.0
1575 silly packumentCache full:https://registry.npmjs.org/path-is-absolute cache-miss
1576 silly fetch manifest brace-expansion@^1.1.7
1577 silly packumentCache full:https://registry.npmjs.org/brace-expansion cache-miss
1578 http cache https://registry.npmjs.org/brace-expansion 4ms (cache hit)
1579 silly packumentCache full:https://registry.npmjs.org/brace-expansion set size:46250 disposed:false
1580 http fetch GET 200 https://registry.npmjs.org/inflight 41ms (cache miss)
1581 silly packumentCache full:https://registry.npmjs.org/inflight set size:undefined disposed:false
1582 http fetch GET 200 https://registry.npmjs.org/inherits 41ms (cache miss)
1583 silly packumentCache full:https://registry.npmjs.org/inherits set size:undefined disposed:false
1584 http fetch GET 200 https://registry.npmjs.org/once 42ms (cache miss)
1585 silly packumentCache full:https://registry.npmjs.org/once set size:undefined disposed:false
1586 http fetch GET 200 https://registry.npmjs.org/fs.realpath 41ms (cache miss)
1587 silly packumentCache full:https://registry.npmjs.org/fs.realpath set size:undefined disposed:false
1588 http fetch GET 200 https://registry.npmjs.org/path-is-absolute 42ms (cache miss)
1589 silly packumentCache full:https://registry.npmjs.org/path-is-absolute set size:undefined disposed:false
1590 silly placeDep ROOT is-number@7.0.0 OK for: to-regex-range@5.0.1 want: ^7.0.0
1591 silly placeDep ROOT @unrs/resolver-binding-android-arm-eabi@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1592 silly placeDep ROOT @unrs/resolver-binding-android-arm64@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1593 silly placeDep ROOT @unrs/resolver-binding-darwin-arm64@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1594 silly placeDep ROOT @unrs/resolver-binding-darwin-x64@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1595 silly placeDep ROOT @unrs/resolver-binding-freebsd-x64@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1596 silly placeDep ROOT @unrs/resolver-binding-linux-arm-gnueabihf@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1597 silly placeDep ROOT @unrs/resolver-binding-linux-arm-musleabihf@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1598 silly placeDep ROOT @unrs/resolver-binding-linux-arm64-gnu@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1599 silly placeDep ROOT @unrs/resolver-binding-linux-arm64-musl@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1600 silly placeDep ROOT @unrs/resolver-binding-linux-ppc64-gnu@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1601 silly placeDep ROOT @unrs/resolver-binding-linux-riscv64-gnu@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1602 silly placeDep ROOT @unrs/resolver-binding-linux-riscv64-musl@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1603 silly placeDep ROOT @unrs/resolver-binding-linux-s390x-gnu@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1604 silly placeDep ROOT @unrs/resolver-binding-linux-x64-gnu@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1605 silly placeDep ROOT @unrs/resolver-binding-linux-x64-musl@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1606 silly placeDep ROOT @unrs/resolver-binding-wasm32-wasi@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1607 silly placeDep ROOT @unrs/resolver-binding-win32-arm64-msvc@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1608 silly placeDep ROOT @unrs/resolver-binding-win32-ia32-msvc@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1609 silly placeDep ROOT @unrs/resolver-binding-win32-x64-msvc@1.11.1 OK for: unrs-resolver@1.11.1 want: 1.11.1
1610 silly placeDep ROOT napi-postinstall@0.3.3 OK for: unrs-resolver@1.11.1 want: ^0.3.0
1611 silly fetch manifest @napi-rs/wasm-runtime@^0.2.11
1612 silly packumentCache full:https://registry.npmjs.org/@napi-rs%2fwasm-runtime cache-miss
1613 http fetch GET 200 https://registry.npmjs.org/@napi-rs%2fwasm-runtime 52ms (cache miss)
1614 silly packumentCache full:https://registry.npmjs.org/@napi-rs%2fwasm-runtime set size:undefined disposed:false
1615 silly placeDep ROOT @napi-rs/wasm-runtime@0.2.12 OK for: @unrs/resolver-binding-wasm32-wasi@1.11.1 want: ^0.2.11
1616 silly fetch manifest @emnapi/core@^1.4.3
1617 silly packumentCache full:https://registry.npmjs.org/@emnapi%2fcore cache-miss
1618 silly fetch manifest @emnapi/runtime@^1.4.3
1619 silly packumentCache full:https://registry.npmjs.org/@emnapi%2fruntime cache-miss
1620 silly fetch manifest @tybys/wasm-util@^0.10.0
1621 silly packumentCache full:https://registry.npmjs.org/@tybys%2fwasm-util cache-miss
1622 http fetch GET 200 https://registry.npmjs.org/@emnapi%2fruntime 194ms (cache miss)
1623 silly packumentCache full:https://registry.npmjs.org/@emnapi%2fruntime set size:undefined disposed:false
1624 http fetch GET 200 https://registry.npmjs.org/@tybys%2fwasm-util 196ms (cache miss)
1625 silly packumentCache full:https://registry.npmjs.org/@tybys%2fwasm-util set size:undefined disposed:false
1626 http fetch GET 200 https://registry.npmjs.org/@emnapi%2fcore 214ms (cache miss)
1627 silly packumentCache full:https://registry.npmjs.org/@emnapi%2fcore set size:undefined disposed:false
1628 silly placeDep ROOT @emnapi/core@1.5.0 OK for: @napi-rs/wasm-runtime@0.2.12 want: ^1.4.3
1629 silly placeDep ROOT @emnapi/runtime@1.5.0 OK for: @napi-rs/wasm-runtime@0.2.12 want: ^1.4.3
1630 silly placeDep ROOT @tybys/wasm-util@0.10.1 OK for: @napi-rs/wasm-runtime@0.2.12 want: ^0.10.0
1631 silly fetch manifest @emnapi/wasi-threads@1.1.0
1632 silly packumentCache full:https://registry.npmjs.org/@emnapi%2fwasi-threads cache-miss
1633 silly fetch manifest tslib@^2.4.0
1634 silly packumentCache full:https://registry.npmjs.org/tslib cache-miss
1635 silly fetch manifest tslib@^2.4.0
1636 silly packumentCache full:https://registry.npmjs.org/tslib cache-miss
1637 silly fetch manifest tslib@^2.4.0
1638 silly packumentCache full:https://registry.npmjs.org/tslib cache-miss
1639 http fetch GET 200 https://registry.npmjs.org/tslib 37ms (cache miss)
1640 silly packumentCache full:https://registry.npmjs.org/tslib set size:undefined disposed:false
1641 http fetch GET 200 https://registry.npmjs.org/tslib 42ms (cache miss)
1642 silly packumentCache full:https://registry.npmjs.org/tslib set size:undefined disposed:false
1643 http fetch GET 200 https://registry.npmjs.org/tslib 42ms (cache miss)
1644 silly packumentCache full:https://registry.npmjs.org/tslib set size:undefined disposed:false
1645 http fetch GET 200 https://registry.npmjs.org/@emnapi%2fwasi-threads 61ms (cache miss)
1646 silly packumentCache full:https://registry.npmjs.org/@emnapi%2fwasi-threads set size:undefined disposed:false
1647 silly placeDep ROOT @emnapi/wasi-threads@1.1.0 OK for: @emnapi/core@1.5.0 want: 1.1.0
1648 silly placeDep ROOT tslib@2.8.1 OK for: @emnapi/core@1.5.0 want: ^2.4.0
1649 silly placeDep ROOT escalade@3.2.0 OK for: update-browserslist-db@1.1.3 want: ^3.2.0
1650 silly placeDep ROOT makeerror@1.0.12 OK for: walker@1.0.8 want: 1.0.12
1651 silly fetch manifest tmpl@1.0.5
1652 silly packumentCache full:https://registry.npmjs.org/tmpl cache-miss
1653 http fetch GET 200 https://registry.npmjs.org/tmpl 40ms (cache miss)
1654 silly packumentCache full:https://registry.npmjs.org/tmpl set size:undefined disposed:false
1655 silly placeDep ROOT tmpl@1.0.5 OK for: makeerror@1.0.12 want: 1.0.5
1656 silly placeDep ROOT isexe@2.0.0 OK for: which@2.0.2 want: ^2.0.0
1657 silly placeDep node_modules/wrap-ansi ansi-styles@6.2.3 OK for: wrap-ansi@8.1.0 want: ^6.1.0
1658 silly placeDep node_modules/wrap-ansi-cjs string-width@4.2.3 OK for: wrap-ansi@7.0.0 want: ^4.1.0
1659 silly placeDep node_modules/wrap-ansi-cjs strip-ansi@6.0.1 OK for: wrap-ansi@7.0.0 want: ^6.0.0
1660 silly placeDep ROOT imurmurhash@0.1.4 OK for: write-file-atomic@5.0.1 want: ^0.1.4
1661 silly placeDep ROOT cliui@8.0.1 OK for: yargs@17.7.2 want: ^8.0.1
1662 silly placeDep ROOT get-caller-file@2.0.5 OK for: yargs@17.7.2 want: ^2.0.5
1663 silly placeDep ROOT require-directory@2.1.1 OK for: yargs@17.7.2 want: ^2.1.1
1664 silly placeDep node_modules/yargs string-width@4.2.3 OK for: yargs@17.7.2 want: ^4.2.3
1665 silly placeDep ROOT y18n@5.0.8 OK for: yargs@17.7.2 want: ^5.0.5
1666 silly placeDep ROOT yargs-parser@21.1.1 OK for: yargs@17.7.2 want: ^21.1.1
1667 silly fetch manifest wrap-ansi@^7.0.0
1668 silly packumentCache full:https://registry.npmjs.org/wrap-ansi cache-miss
1669 silly fetch manifest string-width@^4.2.0
1670 silly packumentCache full:https://registry.npmjs.org/string-width cache-hit
1671 http cache https://registry.npmjs.org/wrap-ansi 2ms (cache hit)
1672 silly packumentCache full:https://registry.npmjs.org/wrap-ansi set size:51195 disposed:false
1673 silly placeDep node_modules/cliui string-width@4.2.3 OK for: cliui@8.0.1 want: ^4.2.0
1674 silly placeDep node_modules/cliui strip-ansi@6.0.1 OK for: cliui@8.0.1 want: ^6.0.1
1675 silly placeDep node_modules/cliui wrap-ansi@7.0.0 OK for: cliui@8.0.1 want: ^7.0.0
1676 silly placeDep node_modules/cliui emoji-regex@8.0.0 OK for: string-width@4.2.3 want: ^8.0.0
1677 silly placeDep node_modules/cliui ansi-regex@5.0.1 OK for: strip-ansi@6.0.1 want: ^5.0.1
1678 silly placeDep ROOT p-try@2.2.0 OK for: p-limit@2.3.0 want: ^2.0.0
1679 silly placeDep node_modules/string-length ansi-regex@5.0.1 OK for: strip-ansi@6.0.1 want: ^5.0.1
1680 silly placeDep node_modules/string-width-cjs ansi-regex@5.0.1 OK for: strip-ansi@6.0.1 want: ^5.0.1
1681 silly placeDep ROOT fs.realpath@1.0.0 OK for: glob@7.2.3 want: ^1.0.0
1682 silly placeDep ROOT inflight@1.0.6 OK for: glob@7.2.3 want: ^1.0.4
1683 silly placeDep ROOT inherits@2.0.4 OK for: glob@7.2.3 want: 2
1684 silly placeDep ROOT once@1.4.0 OK for: glob@7.2.3 want: ^1.3.0
1685 silly placeDep ROOT path-is-absolute@1.0.1 OK for: glob@7.2.3 want: ^1.0.0
1686 silly fetch manifest wrappy@1
1687 silly packumentCache full:https://registry.npmjs.org/wrappy cache-miss
1688 silly fetch manifest wrappy@1
1689 silly packumentCache full:https://registry.npmjs.org/wrappy cache-miss
1690 http fetch GET 200 https://registry.npmjs.org/wrappy 31ms (cache miss)
1691 silly packumentCache full:https://registry.npmjs.org/wrappy set size:undefined disposed:false
1692 http fetch GET 200 https://registry.npmjs.org/wrappy 43ms (cache miss)
1693 silly packumentCache full:https://registry.npmjs.org/wrappy set size:undefined disposed:false
1694 silly placeDep ROOT wrappy@1.0.2 OK for: inflight@1.0.6 want: 1
1695 silly placeDep node_modules/test-exclude brace-expansion@1.1.12 OK for: minimatch@3.1.2 want: ^1.1.7
1696 silly fetch manifest concat-map@0.0.1
1697 silly packumentCache full:https://registry.npmjs.org/concat-map cache-miss
1698 http fetch GET 200 https://registry.npmjs.org/concat-map 48ms (cache miss)
1699 silly packumentCache full:https://registry.npmjs.org/concat-map set size:undefined disposed:false
1700 silly placeDep ROOT concat-map@0.0.1 OK for: brace-expansion@1.1.12 want: 0.0.1
1701 silly placeDep node_modules/wrap-ansi-cjs emoji-regex@8.0.0 OK for: string-width@4.2.3 want: ^8.0.0
1702 silly placeDep node_modules/wrap-ansi-cjs ansi-regex@5.0.1 OK for: strip-ansi@6.0.1 want: ^5.0.1
1703 silly placeDep node_modules/yargs emoji-regex@8.0.0 OK for: string-width@4.2.3 want: ^8.0.0
1704 silly placeDep node_modules/yargs strip-ansi@6.0.1 OK for: string-width@4.2.3 want: ^6.0.1
1705 silly placeDep node_modules/yargs ansi-regex@5.0.1 OK for: strip-ansi@6.0.1 want: ^5.0.1
1706 silly reify moves {}
1707 silly audit bulk request {
1707 silly audit   jest: [ '30.2.0' ],
1707 silly audit   '@jest/core': [ '30.2.0' ],
1707 silly audit   '@jest/types': [ '30.2.0' ],
1707 silly audit   'import-local': [ '3.2.0' ],
1707 silly audit   'jest-cli': [ '30.2.0' ],
1707 silly audit   '@jest/console': [ '30.2.0' ],
1707 silly audit   '@jest/pattern': [ '30.0.1' ],
1707 silly audit   '@jest/reporters': [ '30.2.0' ],
1707 silly audit   '@jest/test-result': [ '30.2.0' ],
1707 silly audit   '@jest/transform': [ '30.2.0' ],
1707 silly audit   '@types/node': [ '24.5.2' ],
1707 silly audit   'ansi-escapes': [ '4.3.2' ],
1707 silly audit   chalk: [ '4.1.2' ],
1707 silly audit   'ci-info': [ '4.3.0' ],
1707 silly audit   'exit-x': [ '0.2.2' ],
1707 silly audit   'graceful-fs': [ '4.2.11' ],
1707 silly audit   'jest-changed-files': [ '30.2.0' ],
1707 silly audit   'jest-config': [ '30.2.0' ],
1707 silly audit   'jest-haste-map': [ '30.2.0' ],
1707 silly audit   'jest-message-util': [ '30.2.0' ],
1707 silly audit   'jest-regex-util': [ '30.0.1' ],
1707 silly audit   'jest-resolve': [ '30.2.0' ],
1707 silly audit   'jest-resolve-dependencies': [ '30.2.0' ],
1707 silly audit   'jest-runner': [ '30.2.0' ],
1707 silly audit   'jest-runtime': [ '30.2.0' ],
1707 silly audit   'jest-snapshot': [ '30.2.0' ],
1707 silly audit   'jest-util': [ '30.2.0' ],
1707 silly audit   'jest-validate': [ '30.2.0' ],
1707 silly audit   'jest-watcher': [ '30.2.0' ],
1707 silly audit   micromatch: [ '4.0.8' ],
1707 silly audit   'pretty-format': [ '30.2.0' ],
1707 silly audit   slash: [ '3.0.0' ],
1707 silly audit   '@bcoe/v8-coverage': [ '0.2.3' ],
1707 silly audit   '@jridgewell/trace-mapping': [ '0.3.31' ],
1707 silly audit   'collect-v8-coverage': [ '1.0.2' ],
1707 silly audit   glob: [ '10.4.5', '7.2.3' ],
1707 silly audit   'istanbul-lib-coverage': [ '3.2.2' ],
1707 silly audit   'istanbul-lib-instrument': [ '6.0.3' ],
1707 silly audit   'istanbul-lib-report': [ '3.0.1' ],
1707 silly audit   'istanbul-lib-source-maps': [ '5.0.6' ],
1707 silly audit   'istanbul-reports': [ '3.2.0' ],
1707 silly audit   'jest-worker': [ '30.2.0' ],
1707 silly audit   'string-length': [ '4.0.2' ],
1707 silly audit   'v8-to-istanbul': [ '9.3.0' ],
1707 silly audit   '@types/istanbul-lib-coverage': [ '2.0.6' ],
1707 silly audit   '@babel/core': [ '7.28.4' ],
1707 silly audit   'babel-plugin-istanbul': [ '7.0.1' ],
1707 silly audit   'convert-source-map': [ '2.0.0' ],
1707 silly audit   'fast-json-stable-stringify': [ '2.1.0' ],
1707 silly audit   pirates: [ '4.0.7' ],
1707 silly audit   'write-file-atomic': [ '5.0.1' ],
1707 silly audit   '@babel/code-frame': [ '7.27.1' ],
1707 silly audit   '@babel/generator': [ '7.28.3' ],
1707 silly audit   '@babel/helper-compilation-targets': [ '7.27.2' ],
1707 silly audit   '@babel/helper-module-transforms': [ '7.28.3' ],
1707 silly audit   '@babel/helpers': [ '7.28.4' ],
1707 silly audit   '@babel/parser': [ '7.28.4' ],
1707 silly audit   '@babel/template': [ '7.27.2' ],
1707 silly audit   '@babel/traverse': [ '7.28.4' ],
1707 silly audit   '@babel/types': [ '7.28.4' ],
1707 silly audit   '@jridgewell/remapping': [ '2.3.5' ],
1707 silly audit   debug: [ '4.4.3' ],
1707 silly audit   gensync: [ '1.0.0-beta.2' ],
1707 silly audit   json5: [ '2.2.3' ],
1707 silly audit   semver: [ '6.3.1', '7.7.2' ],
1707 silly audit   '@babel/helper-validator-identifier': [ '7.27.1' ],
1707 silly audit   'js-tokens': [ '4.0.0' ],
1707 silly audit   picocolors: [ '1.1.1' ],
1707 silly audit   '@jridgewell/gen-mapping': [ '0.3.13' ],
1707 silly audit   jsesc: [ '3.1.0' ],
1707 silly audit   '@babel/compat-data': [ '7.28.4' ],
1707 silly audit   '@babel/helper-validator-option': [ '7.27.1' ],
1707 silly audit   browserslist: [ '4.26.2' ],
1707 silly audit   'lru-cache': [ '5.1.1', '10.4.3' ],
1707 silly audit   '@babel/helper-module-imports': [ '7.27.1' ],
1707 silly audit   '@babel/helper-globals': [ '7.28.0' ],
1707 silly audit   '@babel/helper-string-parser': [ '7.27.1' ],
1707 silly audit   '@jest/schemas': [ '30.0.5' ],
1707 silly audit   '@types/istanbul-reports': [ '3.0.4' ],
1707 silly audit   '@types/yargs': [ '17.0.33' ],
1707 silly audit   '@sinclair/typebox': [ '0.34.41' ],
1707 silly audit   '@jridgewell/sourcemap-codec': [ '1.5.5' ],
1707 silly audit   '@jridgewell/resolve-uri': [ '3.1.2' ],
1707 silly audit   '@types/istanbul-lib-report': [ '3.0.3' ],
1707 silly audit   'undici-types': [ '7.12.0' ],
1707 silly audit   '@types/yargs-parser': [ '21.0.3' ],
1707 silly audit   'type-fest': [ '0.21.3' ],
1707 silly audit   '@babel/helper-plugin-utils': [ '7.27.1' ],
1707 silly audit   '@istanbuljs/load-nyc-config': [ '1.1.0' ],
1707 silly audit   '@istanbuljs/schema': [ '0.1.3' ],
1707 silly audit   'test-exclude': [ '6.0.0' ],
1707 silly audit   camelcase: [ '5.3.1', '6.3.0' ],
1707 silly audit   'find-up': [ '4.1.0' ],
1707 silly audit   'get-package-type': [ '0.1.0' ],
1707 silly audit   'js-yaml': [ '3.14.1' ],
1707 silly audit   'resolve-from': [ '5.0.0' ],
1707 silly audit   'baseline-browser-mapping': [ '2.8.8' ],
1707 silly audit   'caniuse-lite': [ '1.0.30001745' ],
1707 silly audit   'electron-to-chromium': [ '1.5.227' ],
1707 silly audit   'node-releases': [ '2.0.21' ],
1707 silly audit   'update-browserslist-db': [ '1.1.3' ],
1707 silly audit   'ansi-styles': [ '4.3.0', '5.2.0', '6.2.3' ],
1707 silly audit   'supports-color': [ '7.2.0', '8.1.1' ],
1707 silly audit   'color-convert': [ '2.0.1' ],
1707 silly audit   'color-name': [ '1.1.4' ],
1707 silly audit   ms: [ '2.1.3' ],
1707 silly audit   'locate-path': [ '5.0.0' ],
1707 silly audit   'path-exists': [ '4.0.0' ],
1707 silly audit   'foreground-child': [ '3.3.1' ],
1707 silly audit   jackspeak: [ '3.4.3' ],
1707 silly audit   minimatch: [ '9.0.5', '3.1.2' ],
1707 silly audit   minipass: [ '7.1.2' ],
1707 silly audit   'package-json-from-dist': [ '1.0.1' ],
1707 silly audit   'path-scurry': [ '1.11.1' ],
1707 silly audit   'cross-spawn': [ '7.0.6' ],
1707 silly audit   'signal-exit': [ '4.1.0', '3.0.7' ],
1707 silly audit   'path-key': [ '3.1.1' ],
1707 silly audit   'shebang-command': [ '2.0.0' ],
1707 silly audit   which: [ '2.0.2' ],
1707 silly audit   'pkg-dir': [ '4.2.0' ],
1707 silly audit   'resolve-cwd': [ '3.0.0' ],
1707 silly audit   'make-dir': [ '4.0.0' ],
1707 silly audit   'html-escaper': [ '2.0.2' ],
1707 silly audit   '@isaacs/cliui': [ '8.0.2' ],
1707 silly audit   '@pkgjs/parseargs': [ '0.11.0' ],
1707 silly audit   'string-width': [ '5.1.2', '4.2.3' ],
1707 silly audit   'strip-ansi': [ '7.1.2', '6.0.1' ],
1707 silly audit   'wrap-ansi': [ '8.1.0', '7.0.0' ],
1707 silly audit   execa: [ '5.1.1' ],
1707 silly audit   'p-limit': [ '3.1.0', '2.3.0' ],
1707 silly audit   'get-stream': [ '6.0.1' ],
1707 silly audit   'human-signals': [ '2.1.0' ],
1707 silly audit   'is-stream': [ '2.0.1' ],
1707 silly audit   'merge-stream': [ '2.0.0' ],
1707 silly audit   'npm-run-path': [ '4.0.1' ],
1707 silly audit   onetime: [ '5.1.2' ],
1707 silly audit   'strip-final-newline': [ '2.0.0' ],
1707 silly audit   yargs: [ '17.7.2' ],
1707 silly audit   '@jest/get-type': [ '30.1.0' ],
1707 silly audit   '@jest/test-sequencer': [ '30.2.0' ],
1707 silly audit   'babel-jest': [ '30.2.0' ],
1707 silly audit   deepmerge: [ '4.3.1' ],
1707 silly audit   'jest-circus': [ '30.2.0' ],
1707 silly audit   'jest-docblock': [ '30.2.0' ],
1707 silly audit   'jest-environment-node': [ '30.2.0' ],
1707 silly audit   'parse-json': [ '5.2.0' ],
1707 silly audit   'strip-json-comments': [ '3.1.1' ],
1707 silly audit   '@types/babel__core': [ '7.20.5' ],
1707 silly audit   'babel-preset-jest': [ '30.2.0' ],
1707 silly audit   '@types/babel__generator': [ '7.27.0' ],
1707 silly audit   '@types/babel__template': [ '7.4.4' ],
1707 silly audit   '@types/babel__traverse': [ '7.28.0' ],
1707 silly audit   'babel-plugin-jest-hoist': [ '30.2.0' ],
1707 silly audit   'babel-preset-current-node-syntax': [ '1.2.0' ],
1707 silly audit   '@babel/plugin-syntax-async-generators': [ '7.8.4' ],
1707 silly audit   '@babel/plugin-syntax-bigint': [ '7.8.3' ],
1707 silly audit   '@babel/plugin-syntax-class-properties': [ '7.12.13' ],
1707 silly audit   '@babel/plugin-syntax-class-static-block': [ '7.14.5' ],
1707 silly audit   '@babel/plugin-syntax-import-attributes': [ '7.27.1' ],
1707 silly audit   '@babel/plugin-syntax-import-meta': [ '7.10.4' ],
1707 silly audit   '@babel/plugin-syntax-json-strings': [ '7.8.3' ],
1707 silly audit   '@babel/plugin-syntax-logical-assignment-operators': [ '7.10.4' ],
1707 silly audit   '@babel/plugin-syntax-nullish-coalescing-operator': [ '7.8.3' ],
1707 silly audit   '@babel/plugin-syntax-numeric-separator': [ '7.10.4' ],
1707 silly audit   '@babel/plugin-syntax-object-rest-spread': [ '7.8.3' ],
1707 silly audit   '@babel/plugin-syntax-optional-catch-binding': [ '7.8.3' ],
1707 silly audit   '@babel/plugin-syntax-optional-chaining': [ '7.8.3' ],
1707 silly audit   '@babel/plugin-syntax-private-property-in-object': [ '7.14.5' ],
1707 silly audit   '@babel/plugin-syntax-top-level-await': [ '7.14.5' ],
1707 silly audit   '@jest/environment': [ '30.2.0' ],
1707 silly audit   '@jest/expect': [ '30.2.0' ],
1707 silly audit   co: [ '4.6.0' ],
1707 silly audit   dedent: [ '1.7.0' ],
1707 silly audit   'is-generator-fn': [ '2.1.0' ],
1707 silly audit   'jest-each': [ '30.2.0' ],
1707 silly audit   'jest-matcher-utils': [ '30.2.0' ],
1707 silly audit   'pure-rand': [ '7.0.1' ],
1707 silly audit   'stack-utils': [ '2.0.6' ],
1707 silly audit   '@jest/fake-timers': [ '30.2.0' ],
1707 silly audit   'jest-mock': [ '30.2.0' ],
1707 silly audit   expect: [ '30.2.0' ],
1707 silly audit   '@sinonjs/fake-timers': [ '13.0.5' ],
1707 silly audit   '@sinonjs/commons': [ '3.0.1' ],
1707 silly audit   'type-detect': [ '4.0.8' ],
1707 silly audit   '@jest/expect-utils': [ '30.2.0' ],
1707 silly audit   'detect-newline': [ '3.1.0' ],
1707 silly audit   anymatch: [ '3.1.3' ],
1707 silly audit   'fb-watchman': [ '2.0.2' ],
1707 silly audit   fsevents: [ '2.3.3' ],
1707 silly audit   walker: [ '1.0.8' ],
1707 silly audit   'normalize-path': [ '3.0.0' ],
1707 silly audit   picomatch: [ '2.3.1', '4.0.3' ],
1707 silly audit   bser: [ '2.1.1' ],
1707 silly audit   'node-int64': [ '0.4.0' ],
1707 silly audit   'jest-diff': [ '30.2.0' ],
1707 silly audit   '@jest/diff-sequences': [ '30.0.1' ],
1707 silly audit   '@types/stack-utils': [ '2.0.3' ],
1707 silly audit   'jest-pnp-resolver': [ '1.2.3' ],
1707 silly audit   'unrs-resolver': [ '1.11.1' ],
1707 silly audit   emittery: [ '0.13.1' ],
1707 silly audit   'jest-leak-detector': [ '30.2.0' ],
1707 silly audit   'source-map-support': [ '0.5.13' ],
1707 silly audit   '@jest/globals': [ '30.2.0' ],
1707 silly audit   '@jest/source-map': [ '30.0.1' ],
1707 silly audit   'cjs-module-lexer': [ '2.1.0' ],
1707 silly audit   'strip-bom': [ '4.0.0' ],
1707 silly audit   callsites: [ '3.1.0' ],
1707 silly audit   '@babel/plugin-syntax-jsx': [ '7.27.1' ],
1707 silly audit   '@babel/plugin-syntax-typescript': [ '7.27.1' ],
1707 silly audit   '@jest/snapshot-utils': [ '30.2.0' ],
1707 silly audit   synckit: [ '0.11.11' ],
1707 silly audit   'natural-compare': [ '1.4.0' ],
1707 silly audit   leven: [ '3.1.0' ],
1707 silly audit   '@ungap/structured-clone': [ '1.3.0' ],
1707 silly audit   argparse: [ '1.0.10' ],
1707 silly audit   esprima: [ '4.0.1' ],
1707 silly audit   'sprintf-js': [ '1.0.3' ],
1707 silly audit   'p-locate': [ '4.1.0' ],
1707 silly audit   yallist: [ '3.1.1' ],
1707 silly audit   braces: [ '3.0.3' ],
1707 silly audit   'fill-range': [ '7.1.1' ],
1707 silly audit   'to-regex-range': [ '5.0.1' ],
1707 silly audit   'brace-expansion': [ '2.0.2', '1.1.12' ],
1707 silly audit   'balanced-match': [ '1.0.2' ],
1707 silly audit   'mimic-fn': [ '2.1.0' ],
1707 silly audit   'yocto-queue': [ '0.1.0' ],
1707 silly audit   'error-ex': [ '1.3.4' ],
1707 silly audit   'json-parse-even-better-errors': [ '2.3.1' ],
1707 silly audit   'lines-and-columns': [ '1.2.4' ],
1707 silly audit   'is-arrayish': [ '0.2.1' ],
1707 silly audit   'react-is': [ '18.3.1' ],
1707 silly audit   'shebang-regex': [ '3.0.0' ],
1707 silly audit   'buffer-from': [ '1.1.2' ],
1707 silly audit   'source-map': [ '0.6.1' ],
1707 silly audit   'escape-string-regexp': [ '2.0.0' ],
1707 silly audit   'char-regex': [ '1.0.2' ],
1707 silly audit   eastasianwidth: [ '0.2.0' ],
1707 silly audit   'emoji-regex': [ '9.2.2', '8.0.0' ],
1707 silly audit   'is-fullwidth-code-point': [ '3.0.0' ],
1707 silly audit   'ansi-regex': [ '6.2.2', '5.0.1' ],
1707 silly audit   'has-flag': [ '4.0.0' ],
1707 silly audit   '@pkgr/core': [ '0.2.9' ],
1707 silly audit   'is-number': [ '7.0.0' ],
1707 silly audit   '@unrs/resolver-binding-android-arm-eabi': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-android-arm64': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-darwin-arm64': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-darwin-x64': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-freebsd-x64': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-linux-arm-gnueabihf': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-linux-arm-musleabihf': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-linux-arm64-gnu': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-linux-arm64-musl': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-linux-ppc64-gnu': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-linux-riscv64-gnu': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-linux-riscv64-musl': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-linux-s390x-gnu': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-linux-x64-gnu': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-linux-x64-musl': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-wasm32-wasi': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-win32-arm64-msvc': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-win32-ia32-msvc': [ '1.11.1' ],
1707 silly audit   '@unrs/resolver-binding-win32-x64-msvc': [ '1.11.1' ],
1707 silly audit   'napi-postinstall': [ '0.3.3' ],
1707 silly audit   '@napi-rs/wasm-runtime': [ '0.2.12' ],
1707 silly audit   '@emnapi/core': [ '1.5.0' ],
1707 silly audit   '@emnapi/runtime': [ '1.5.0' ],
1707 silly audit   '@tybys/wasm-util': [ '0.10.1' ],
1707 silly audit   '@emnapi/wasi-threads': [ '1.1.0' ],
1707 silly audit   tslib: [ '2.8.1' ],
1707 silly audit   escalade: [ '3.2.0' ],
1707 silly audit   makeerror: [ '1.0.12' ],
1707 silly audit   tmpl: [ '1.0.5' ],
1707 silly audit   isexe: [ '2.0.0' ],
1707 silly audit   imurmurhash: [ '0.1.4' ],
1707 silly audit   cliui: [ '8.0.1' ],
1707 silly audit   'get-caller-file': [ '2.0.5' ],
1707 silly audit   'require-directory': [ '2.1.1' ],
1707 silly audit   y18n: [ '5.0.8' ],
1707 silly audit   'yargs-parser': [ '21.1.1' ],
1707 silly audit   'p-try': [ '2.2.0' ],
1707 silly audit   'fs.realpath': [ '1.0.0' ],
1707 silly audit   inflight: [ '1.0.6' ],
1707 silly audit   inherits: [ '2.0.4' ],
1707 silly audit   once: [ '1.4.0' ],
1707 silly audit   'path-is-absolute': [ '1.0.1' ],
1707 silly audit   wrappy: [ '1.0.2' ],
1707 silly audit   'concat-map': [ '0.0.1' ]
1707 silly audit }
1708 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-win32-x64-msvc
1709 silly reify mark deleted [
1709 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-win32-x64-msvc'
1709 silly reify ]
1710 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-win32-ia32-msvc
1711 silly reify mark deleted [
1711 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-win32-ia32-msvc'
1711 silly reify ]
1712 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-win32-arm64-msvc
1713 silly reify mark deleted [
1713 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-win32-arm64-msvc'
1713 silly reify ]
1714 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-wasm32-wasi
1715 silly reify mark deleted [
1715 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-wasm32-wasi'
1715 silly reify ]
1716 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@napi-rs/wasm-runtime
1717 silly reify mark deleted [
1717 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@napi-rs/wasm-runtime'
1717 silly reify ]
1718 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@emnapi/core
1719 silly reify mark deleted [
1719 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@emnapi/core'
1719 silly reify ]
1720 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@emnapi/runtime
1721 silly reify mark deleted [
1721 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@emnapi/runtime'
1721 silly reify ]
1722 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@tybys/wasm-util
1723 silly reify mark deleted [
1723 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@tybys/wasm-util'
1723 silly reify ]
1724 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@emnapi/wasi-threads
1725 silly reify mark deleted [
1725 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@emnapi/wasi-threads'
1725 silly reify ]
1726 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/tslib
1727 silly reify mark deleted [
1727 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/tslib'
1727 silly reify ]
1728 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-x64-musl
1729 silly reify mark deleted [
1729 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-x64-musl'
1729 silly reify ]
1730 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-x64-gnu
1731 silly reify mark deleted [
1731 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-x64-gnu'
1731 silly reify ]
1732 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-s390x-gnu
1733 silly reify mark deleted [
1733 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-s390x-gnu'
1733 silly reify ]
1734 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-riscv64-musl
1735 silly reify mark deleted [
1735 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-riscv64-musl'
1735 silly reify ]
1736 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-riscv64-gnu
1737 silly reify mark deleted [
1737 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-riscv64-gnu'
1737 silly reify ]
1738 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-ppc64-gnu
1739 silly reify mark deleted [
1739 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-ppc64-gnu'
1739 silly reify ]
1740 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-arm64-musl
1741 silly reify mark deleted [
1741 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-arm64-musl'
1741 silly reify ]
1742 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-arm64-gnu
1743 silly reify mark deleted [
1743 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-arm64-gnu'
1743 silly reify ]
1744 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-arm-musleabihf
1745 silly reify mark deleted [
1745 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-arm-musleabihf'
1745 silly reify ]
1746 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-arm-gnueabihf
1747 silly reify mark deleted [
1747 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-linux-arm-gnueabihf'
1747 silly reify ]
1748 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-freebsd-x64
1749 silly reify mark deleted [
1749 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-freebsd-x64'
1749 silly reify ]
1750 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-darwin-x64
1751 silly reify mark deleted [
1751 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-darwin-x64'
1751 silly reify ]
1752 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-android-arm64
1753 silly reify mark deleted [
1753 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-android-arm64'
1753 silly reify ]
1754 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-android-arm-eabi
1755 silly reify mark deleted [
1755 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/@unrs/resolver-binding-android-arm-eabi'
1755 silly reify ]
1756 http cache concat-map@https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz 0ms (cache hit)
1757 http cache wrappy@https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz 0ms (cache hit)
1758 http cache once@https://registry.npmjs.org/once/-/once-1.4.0.tgz 0ms (cache hit)
1759 http cache inherits@https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz 0ms (cache hit)
1760 http cache inflight@https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz 0ms (cache hit)
1761 http cache fs.realpath@https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz 0ms (cache hit)
1762 http cache p-try@https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz 0ms (cache hit)
1763 http cache path-is-absolute@https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz 0ms (cache hit)
1764 http cache yargs-parser@https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz 0ms (cache hit)
1765 http cache y18n@https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz 0ms (cache hit)
1766 http cache require-directory@https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz 0ms (cache hit)
1767 http cache imurmurhash@https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz 0ms (cache hit)
1768 http cache get-caller-file@https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz 0ms (cache hit)
1769 http cache tmpl@https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz 0ms (cache hit)
1770 http cache cliui@https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz 0ms (cache hit)
1771 http cache isexe@https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz 0ms (cache hit)
1772 http cache escalade@https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz 0ms (cache hit)
1773 http cache @tybys/wasm-util@https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.10.1.tgz 0ms (cache hit)
1774 http cache tslib@https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz 0ms (cache hit)
1775 http cache @emnapi/wasi-threads@https://registry.npmjs.org/@emnapi/wasi-threads/-/wasi-threads-1.1.0.tgz 0ms (cache hit)
1776 http cache @emnapi/runtime@https://registry.npmjs.org/@emnapi/runtime/-/runtime-1.5.0.tgz 0ms (cache hit)
1777 http cache makeerror@https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz 0ms (cache hit)
1778 http cache napi-postinstall@https://registry.npmjs.org/napi-postinstall/-/napi-postinstall-0.3.3.tgz 0ms (cache hit)
1779 http cache @emnapi/core@https://registry.npmjs.org/@emnapi/core/-/core-1.5.0.tgz 0ms (cache hit)
1780 http cache is-number@https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz 0ms (cache hit)
1781 http cache @pkgr/core@https://registry.npmjs.org/@pkgr/core/-/core-0.2.9.tgz 0ms (cache hit)
1782 http cache has-flag@https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz 0ms (cache hit)
1783 http cache ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.2.2.tgz 0ms (cache hit)
1784 http cache is-fullwidth-code-point@https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz 0ms (cache hit)
1785 http cache emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz 0ms (cache hit)
1786 http cache eastasianwidth@https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz 0ms (cache hit)
1787 http cache @napi-rs/wasm-runtime@https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.12.tgz 0ms (cache hit)
1788 http cache char-regex@https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz 0ms (cache hit)
1789 http cache @unrs/resolver-binding-darwin-arm64@https://registry.npmjs.org/@unrs/resolver-binding-darwin-arm64/-/resolver-binding-darwin-arm64-1.11.1.tgz 0ms (cache hit)
1790 http cache source-map@https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz 0ms (cache hit)
1791 http cache escape-string-regexp@https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz 0ms (cache hit)
1792 http cache shebang-regex@https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz 0ms (cache hit)
1793 http cache react-is@https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz 0ms (cache hit)
1794 http cache buffer-from@https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz 0ms (cache hit)
1795 http cache is-arrayish@https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz 0ms (cache hit)
1796 http cache lines-and-columns@https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz 0ms (cache hit)
1797 http cache json-parse-even-better-errors@https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz 0ms (cache hit)
1798 http cache mimic-fn@https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz 0ms (cache hit)
1799 http cache balanced-match@https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz 0ms (cache hit)
1800 http cache brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz 0ms (cache hit)
1801 http cache fill-range@https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz 0ms (cache hit)
1802 http cache braces@https://registry.npmjs.org/braces/-/braces-3.0.3.tgz 0ms (cache hit)
1803 http cache yallist@https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz 0ms (cache hit)
1804 http cache error-ex@https://registry.npmjs.org/error-ex/-/error-ex-1.3.4.tgz 0ms (cache hit)
1805 http cache p-locate@https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz 0ms (cache hit)
1806 http cache sprintf-js@https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz 0ms (cache hit)
1807 http cache esprima@https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz 0ms (cache hit)
1808 http cache to-regex-range@https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz 0ms (cache hit)
1809 http cache argparse@https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz 0ms (cache hit)
1810 http cache @ungap/structured-clone@https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz 0ms (cache hit)
1811 http cache yocto-queue@https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz 0ms (cache hit)
1812 http cache synckit@https://registry.npmjs.org/synckit/-/synckit-0.11.11.tgz 0ms (cache hit)
1813 http cache leven@https://registry.npmjs.org/leven/-/leven-3.1.0.tgz 0ms (cache hit)
1814 http cache @jest/snapshot-utils@https://registry.npmjs.org/@jest/snapshot-utils/-/snapshot-utils-30.2.0.tgz 0ms (cache hit)
1815 http cache @babel/plugin-syntax-jsx@https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz 0ms (cache hit)
1816 http cache @babel/plugin-syntax-typescript@https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz 0ms (cache hit)
1817 http cache strip-bom@https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz 0ms (cache hit)
1818 http cache natural-compare@https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz 0ms (cache hit)
1819 http cache @jest/source-map@https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.1.tgz 0ms (cache hit)
1820 http cache @jest/globals@https://registry.npmjs.org/@jest/globals/-/globals-30.2.0.tgz 0ms (cache hit)
1821 http cache source-map-support@https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz 0ms (cache hit)
1822 http cache jest-leak-detector@https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.2.0.tgz 0ms (cache hit)
1823 http cache unrs-resolver@https://registry.npmjs.org/unrs-resolver/-/unrs-resolver-1.11.1.tgz 0ms (cache hit)
1824 http cache jest-pnp-resolver@https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz 0ms (cache hit)
1825 http cache callsites@https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz 0ms (cache hit)
1826 http cache @types/stack-utils@https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz 0ms (cache hit)
1827 http cache @jest/diff-sequences@https://registry.npmjs.org/@jest/diff-sequences/-/diff-sequences-30.0.1.tgz 0ms (cache hit)
1828 http cache jest-diff@https://registry.npmjs.org/jest-diff/-/jest-diff-30.2.0.tgz 0ms (cache hit)
1829 http cache emittery@https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz 0ms (cache hit)
1830 http cache bser@https://registry.npmjs.org/bser/-/bser-2.1.1.tgz 0ms (cache hit)
1831 http cache cjs-module-lexer@https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-2.1.0.tgz 0ms (cache hit)
1832 http cache node-int64@https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz 0ms (cache hit)
1833 http cache walker@https://registry.npmjs.org/walker/-/walker-1.0.8.tgz 0ms (cache hit)
1834 http cache picomatch@https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz 0ms (cache hit)
1835 http cache normalize-path@https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz 0ms (cache hit)
1836 http cache fb-watchman@https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz 0ms (cache hit)
1837 http cache anymatch@https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz 0ms (cache hit)
1838 http cache detect-newline@https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz 0ms (cache hit)
1839 http cache fsevents@https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz 0ms (cache hit)
1840 http cache @jest/expect-utils@https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.2.0.tgz 0ms (cache hit)
1841 http cache @sinonjs/commons@https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz 0ms (cache hit)
1842 http cache @sinonjs/fake-timers@https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-13.0.5.tgz 0ms (cache hit)
1843 http cache expect@https://registry.npmjs.org/expect/-/expect-30.2.0.tgz 0ms (cache hit)
1844 http cache jest-mock@https://registry.npmjs.org/jest-mock/-/jest-mock-30.2.0.tgz 0ms (cache hit)
1845 http cache @jest/fake-timers@https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.2.0.tgz 0ms (cache hit)
1846 http cache stack-utils@https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz 0ms (cache hit)
1847 http cache pure-rand@https://registry.npmjs.org/pure-rand/-/pure-rand-7.0.1.tgz 0ms (cache hit)
1848 http cache jest-matcher-utils@https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-30.2.0.tgz 0ms (cache hit)
1849 http cache jest-each@https://registry.npmjs.org/jest-each/-/jest-each-30.2.0.tgz 0ms (cache hit)
1850 http cache is-generator-fn@https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz 0ms (cache hit)
1851 http cache dedent@https://registry.npmjs.org/dedent/-/dedent-1.7.0.tgz 0ms (cache hit)
1852 http cache co@https://registry.npmjs.org/co/-/co-4.6.0.tgz 0ms (cache hit)
1853 http cache @jest/expect@https://registry.npmjs.org/@jest/expect/-/expect-30.2.0.tgz 0ms (cache hit)
1854 http cache @jest/environment@https://registry.npmjs.org/@jest/environment/-/environment-30.2.0.tgz 0ms (cache hit)
1855 http cache @babel/plugin-syntax-top-level-await@https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz 0ms (cache hit)
1856 http cache @babel/plugin-syntax-private-property-in-object@https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz 0ms (cache hit)
1857 http cache @babel/plugin-syntax-optional-chaining@https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz 0ms (cache hit)
1858 http cache @babel/plugin-syntax-optional-catch-binding@https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz 0ms (cache hit)
1859 http cache @babel/plugin-syntax-object-rest-spread@https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz 0ms (cache hit)
1860 http cache @babel/plugin-syntax-numeric-separator@https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz 0ms (cache hit)
1861 http cache @babel/plugin-syntax-nullish-coalescing-operator@https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz 0ms (cache hit)
1862 http cache @babel/plugin-syntax-logical-assignment-operators@https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz 0ms (cache hit)
1863 http cache @babel/plugin-syntax-json-strings@https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz 0ms (cache hit)
1864 http cache @babel/plugin-syntax-import-meta@https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz 0ms (cache hit)
1865 http cache @babel/plugin-syntax-import-attributes@https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz 0ms (cache hit)
1866 http cache @babel/plugin-syntax-class-static-block@https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz 0ms (cache hit)
1867 http cache @babel/plugin-syntax-class-properties@https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz 0ms (cache hit)
1868 http cache @babel/plugin-syntax-bigint@https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz 0ms (cache hit)
1869 http cache @babel/plugin-syntax-async-generators@https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz 0ms (cache hit)
1870 http cache babel-preset-current-node-syntax@https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.2.0.tgz 0ms (cache hit)
1871 http cache babel-plugin-jest-hoist@https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.2.0.tgz 0ms (cache hit)
1872 http cache @types/babel__traverse@https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.28.0.tgz 0ms (cache hit)
1873 http cache @types/babel__template@https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz 0ms (cache hit)
1874 http cache @types/babel__generator@https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz 0ms (cache hit)
1875 http cache babel-preset-jest@https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.2.0.tgz 0ms (cache hit)
1876 http cache @types/babel__core@https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz 0ms (cache hit)
1877 http cache strip-json-comments@https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz 0ms (cache hit)
1878 http cache parse-json@https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz 0ms (cache hit)
1879 http cache jest-environment-node@https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-30.2.0.tgz 0ms (cache hit)
1880 http cache jest-docblock@https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.2.0.tgz 0ms (cache hit)
1881 http cache jest-circus@https://registry.npmjs.org/jest-circus/-/jest-circus-30.2.0.tgz 0ms (cache hit)
1882 http cache deepmerge@https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz 0ms (cache hit)
1883 http cache babel-jest@https://registry.npmjs.org/babel-jest/-/babel-jest-30.2.0.tgz 0ms (cache hit)
1884 http cache @jest/test-sequencer@https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.2.0.tgz 0ms (cache hit)
1885 http cache yargs@https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz 0ms (cache hit)
1886 http cache @jest/get-type@https://registry.npmjs.org/@jest/get-type/-/get-type-30.1.0.tgz 0ms (cache hit)
1887 http cache strip-final-newline@https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz 0ms (cache hit)
1888 http cache npm-run-path@https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz 1ms (cache hit)
1889 http cache onetime@https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz 0ms (cache hit)
1890 http cache merge-stream@https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz 0ms (cache hit)
1891 http cache is-stream@https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz 0ms (cache hit)
1892 http cache get-stream@https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz 0ms (cache hit)
1893 http cache human-signals@https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz 0ms (cache hit)
1894 http cache execa@https://registry.npmjs.org/execa/-/execa-5.1.1.tgz 0ms (cache hit)
1895 http cache p-limit@https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz 0ms (cache hit)
1896 http cache wrap-ansi-cjs@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz 0ms (cache hit)
1897 http cache wrap-ansi@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz 0ms (cache hit)
1898 http cache strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.2.tgz 0ms (cache hit)
1899 http cache strip-ansi-cjs@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 0ms (cache hit)
1900 http cache string-width@https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz 0ms (cache hit)
1901 http cache string-width-cjs@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 0ms (cache hit)
1902 http cache @pkgjs/parseargs@https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz 0ms (cache hit)
1903 http cache @isaacs/cliui@https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz 0ms (cache hit)
1904 http cache html-escaper@https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz 0ms (cache hit)
1905 http cache make-dir@https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz 0ms (cache hit)
1906 http cache pkg-dir@https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz 0ms (cache hit)
1907 http cache resolve-cwd@https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz 0ms (cache hit)
1908 http cache shebang-command@https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz 0ms (cache hit)
1909 http cache which@https://registry.npmjs.org/which/-/which-2.0.2.tgz 0ms (cache hit)
1910 http cache signal-exit@https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz 0ms (cache hit)
1911 http cache path-key@https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz 0ms (cache hit)
1912 http cache cross-spawn@https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz 0ms (cache hit)
1913 http cache path-scurry@https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz 0ms (cache hit)
1914 http cache package-json-from-dist@https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz 0ms (cache hit)
1915 http cache minipass@https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz 0ms (cache hit)
1916 http cache jackspeak@https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz 0ms (cache hit)
1917 http cache minimatch@https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz 0ms (cache hit)
1918 http cache foreground-child@https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz 0ms (cache hit)
1919 http cache locate-path@https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz 0ms (cache hit)
1920 http cache path-exists@https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz 0ms (cache hit)
1921 http cache ms@https://registry.npmjs.org/ms/-/ms-2.1.3.tgz 0ms (cache hit)
1922 http cache color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz 0ms (cache hit)
1923 http cache color-convert@https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz 0ms (cache hit)
1924 http cache supports-color@https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz 0ms (cache hit)
1925 http cache ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz 0ms (cache hit)
1926 http cache update-browserslist-db@https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz 0ms (cache hit)
1927 http cache node-releases@https://registry.npmjs.org/node-releases/-/node-releases-2.0.21.tgz 0ms (cache hit)
1928 http cache electron-to-chromium@https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.227.tgz 0ms (cache hit)
1929 http cache caniuse-lite@https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001745.tgz 0ms (cache hit)
1930 http cache baseline-browser-mapping@https://registry.npmjs.org/baseline-browser-mapping/-/baseline-browser-mapping-2.8.8.tgz 0ms (cache hit)
1931 http cache resolve-from@https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz 0ms (cache hit)
1932 http cache js-yaml@https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz 0ms (cache hit)
1933 http cache get-package-type@https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz 0ms (cache hit)
1934 http cache find-up@https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz 0ms (cache hit)
1935 http cache camelcase@https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz 0ms (cache hit)
1936 http cache @istanbuljs/schema@https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz 0ms (cache hit)
1937 http cache test-exclude@https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz 0ms (cache hit)
1938 http cache @istanbuljs/load-nyc-config@https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz 0ms (cache hit)
1939 http cache @babel/helper-plugin-utils@https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz 0ms (cache hit)
1940 http cache type-fest@https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz 0ms (cache hit)
1941 http cache @types/yargs-parser@https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz 0ms (cache hit)
1942 http cache undici-types@https://registry.npmjs.org/undici-types/-/undici-types-7.12.0.tgz 0ms (cache hit)
1943 http cache @types/istanbul-lib-report@https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz 0ms (cache hit)
1944 http cache @jridgewell/sourcemap-codec@https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.5.tgz 0ms (cache hit)
1945 http cache @jridgewell/resolve-uri@https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz 0ms (cache hit)
1946 http cache @sinclair/typebox@https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.34.41.tgz 0ms (cache hit)
1947 http cache @types/yargs@https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz 0ms (cache hit)
1948 http cache @jest/schemas@https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.5.tgz 0ms (cache hit)
1949 http cache @types/istanbul-reports@https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz 0ms (cache hit)
1950 http cache @babel/helper-string-parser@https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz 0ms (cache hit)
1951 http cache @babel/helper-globals@https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz 0ms (cache hit)
1952 http cache lru-cache@https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz 0ms (cache hit)
1953 http cache @babel/helper-module-imports@https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz 0ms (cache hit)
1954 http cache browserslist@https://registry.npmjs.org/browserslist/-/browserslist-4.26.2.tgz 0ms (cache hit)
1955 http cache @babel/compat-data@https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.4.tgz 0ms (cache hit)
1956 http cache jsesc@https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz 0ms (cache hit)
1957 http cache @babel/helper-validator-option@https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz 0ms (cache hit)
1958 http cache picocolors@https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz 0ms (cache hit)
1959 http cache @jridgewell/gen-mapping@https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.13.tgz 0ms (cache hit)
1960 http cache @babel/helper-validator-identifier@https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz 0ms (cache hit)
1961 http cache js-tokens@https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz 0ms (cache hit)
1962 http cache semver@https://registry.npmjs.org/semver/-/semver-6.3.1.tgz 0ms (cache hit)
1963 http cache json5@https://registry.npmjs.org/json5/-/json5-2.2.3.tgz 0ms (cache hit)
1964 http cache gensync@https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz 0ms (cache hit)
1965 http cache debug@https://registry.npmjs.org/debug/-/debug-4.4.3.tgz 0ms (cache hit)
1966 http cache @babel/types@https://registry.npmjs.org/@babel/types/-/types-7.28.4.tgz 0ms (cache hit)
1967 http cache @jridgewell/remapping@https://registry.npmjs.org/@jridgewell/remapping/-/remapping-2.3.5.tgz 0ms (cache hit)
1968 http cache @babel/traverse@https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.4.tgz 0ms (cache hit)
1969 http cache @babel/template@https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz 0ms (cache hit)
1970 http cache @babel/helpers@https://registry.npmjs.org/@babel/helpers/-/helpers-7.28.4.tgz 0ms (cache hit)
1971 http cache @babel/parser@https://registry.npmjs.org/@babel/parser/-/parser-7.28.4.tgz 0ms (cache hit)
1972 http cache @babel/helper-module-transforms@https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.28.3.tgz 0ms (cache hit)
1973 http cache @babel/helper-compilation-targets@https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz 0ms (cache hit)
1974 http cache @babel/generator@https://registry.npmjs.org/@babel/generator/-/generator-7.28.3.tgz 0ms (cache hit)
1975 http cache write-file-atomic@https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-5.0.1.tgz 0ms (cache hit)
1976 http cache pirates@https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz 0ms (cache hit)
1977 http cache fast-json-stable-stringify@https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz 0ms (cache hit)
1978 http cache @babel/core@https://registry.npmjs.org/@babel/core/-/core-7.28.4.tgz 0ms (cache hit)
1979 http cache babel-plugin-istanbul@https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-7.0.1.tgz 0ms (cache hit)
1980 http cache v8-to-istanbul@https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz 0ms (cache hit)
1981 http cache @types/istanbul-lib-coverage@https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz 0ms (cache hit)
1982 http cache string-length@https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz 0ms (cache hit)
1983 http cache jest-worker@https://registry.npmjs.org/jest-worker/-/jest-worker-30.2.0.tgz 0ms (cache hit)
1984 http cache istanbul-reports@https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.2.0.tgz 0ms (cache hit)
1985 http cache istanbul-lib-source-maps@https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-5.0.6.tgz 0ms (cache hit)
1986 http cache istanbul-lib-report@https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz 0ms (cache hit)
1987 http cache @babel/code-frame@https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz 0ms (cache hit)
1988 http cache convert-source-map@https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz 0ms (cache hit)
1989 http cache istanbul-lib-coverage@https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz 0ms (cache hit)
1990 http cache istanbul-lib-instrument@https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz 0ms (cache hit)
1991 http cache glob@https://registry.npmjs.org/glob/-/glob-10.4.5.tgz 0ms (cache hit)
1992 http cache collect-v8-coverage@https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz 0ms (cache hit)
1993 http cache @jridgewell/trace-mapping@https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.31.tgz 0ms (cache hit)
1994 http cache @bcoe/v8-coverage@https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz 0ms (cache hit)
1995 http cache slash@https://registry.npmjs.org/slash/-/slash-3.0.0.tgz 0ms (cache hit)
1996 http cache pretty-format@https://registry.npmjs.org/pretty-format/-/pretty-format-30.2.0.tgz 0ms (cache hit)
1997 http cache micromatch@https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz 0ms (cache hit)
1998 http cache jest-watcher@https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.2.0.tgz 0ms (cache hit)
1999 http cache jest-validate@https://registry.npmjs.org/jest-validate/-/jest-validate-30.2.0.tgz 0ms (cache hit)
2000 http cache jest-util@https://registry.npmjs.org/jest-util/-/jest-util-30.2.0.tgz 0ms (cache hit)
2001 http cache jest-snapshot@https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-30.2.0.tgz 0ms (cache hit)
2002 http cache jest-runtime@https://registry.npmjs.org/jest-runtime/-/jest-runtime-30.2.0.tgz 0ms (cache hit)
2003 http cache jest-resolve-dependencies@https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-30.2.0.tgz 0ms (cache hit)
2004 http cache jest-resolve@https://registry.npmjs.org/jest-resolve/-/jest-resolve-30.2.0.tgz 0ms (cache hit)
2005 http cache jest-runner@https://registry.npmjs.org/jest-runner/-/jest-runner-30.2.0.tgz 0ms (cache hit)
2006 http cache jest-haste-map@https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-30.2.0.tgz 0ms (cache hit)
2007 http cache jest-message-util@https://registry.npmjs.org/jest-message-util/-/jest-message-util-30.2.0.tgz 0ms (cache hit)
2008 http cache jest-regex-util@https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.1.tgz 0ms (cache hit)
2009 http cache jest-changed-files@https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.2.0.tgz 0ms (cache hit)
2010 http cache graceful-fs@https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz 0ms (cache hit)
2011 http cache jest-config@https://registry.npmjs.org/jest-config/-/jest-config-30.2.0.tgz 0ms (cache hit)
2012 http cache ci-info@https://registry.npmjs.org/ci-info/-/ci-info-4.3.0.tgz 0ms (cache hit)
2013 http cache ansi-escapes@https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz 0ms (cache hit)
2014 http cache chalk@https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz 0ms (cache hit)
2015 http cache exit-x@https://registry.npmjs.org/exit-x/-/exit-x-0.2.2.tgz 0ms (cache hit)
2016 http cache @types/node@https://registry.npmjs.org/@types/node/-/node-24.5.2.tgz 0ms (cache hit)
2017 http cache @jest/console@https://registry.npmjs.org/@jest/console/-/console-30.2.0.tgz 0ms (cache hit)
2018 http cache @jest/reporters@https://registry.npmjs.org/@jest/reporters/-/reporters-30.2.0.tgz 0ms (cache hit)
2019 http cache type-detect@https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz 0ms (cache hit)
2020 http cache jest-cli@https://registry.npmjs.org/jest-cli/-/jest-cli-30.2.0.tgz 0ms (cache hit)
2021 http cache @jest/pattern@https://registry.npmjs.org/@jest/pattern/-/pattern-30.0.1.tgz 0ms (cache hit)
2022 http cache @jest/core@https://registry.npmjs.org/@jest/core/-/core-30.2.0.tgz 0ms (cache hit)
2023 http cache @jest/types@https://registry.npmjs.org/@jest/types/-/types-30.2.0.tgz 0ms (cache hit)
2024 http cache import-local@https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz 0ms (cache hit)
2025 http cache jest@https://registry.npmjs.org/jest/-/jest-30.2.0.tgz 0ms (cache hit)
2026 http cache @jest/transform@https://registry.npmjs.org/@jest/transform/-/transform-30.2.0.tgz 0ms (cache hit)
2027 http cache emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 0ms (cache hit)
2028 http cache ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 0ms (cache hit)
2029 http cache wrap-ansi@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz 0ms (cache hit)
2030 http cache string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 0ms (cache hit)
2031 http cache @jest/test-result@https://registry.npmjs.org/@jest/test-result/-/test-result-30.2.0.tgz 0ms (cache hit)
2032 http cache strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 0ms (cache hit)
2033 http cache p-limit@https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz 0ms (cache hit)
2034 http cache emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 0ms (cache hit)
2035 http cache string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 0ms (cache hit)
2036 http cache signal-exit@https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz 0ms (cache hit)
2037 http cache ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 0ms (cache hit)
2038 http cache emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 0ms (cache hit)
2039 http cache string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 0ms (cache hit)
2040 http cache ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.3.tgz 0ms (cache hit)
2041 http cache ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 0ms (cache hit)
2042 http cache ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 0ms (cache hit)
2043 http cache strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 0ms (cache hit)
2044 http cache strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 0ms (cache hit)
2045 http cache emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 0ms (cache hit)
2046 http cache semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 0ms (cache hit)
2047 http cache lru-cache@https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz 0ms (cache hit)
2048 http cache minimatch@https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz 0ms (cache hit)
2049 http cache brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz 0ms (cache hit)
2050 http cache ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 0ms (cache hit)
2051 http cache glob@https://registry.npmjs.org/glob/-/glob-7.2.3.tgz 0ms (cache hit)
2052 http cache strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 0ms (cache hit)
2053 http cache supports-color@https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz 0ms (cache hit)
2054 http cache semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 0ms (cache hit)
2055 http cache picomatch@https://registry.npmjs.org/picomatch/-/picomatch-4.0.3.tgz 0ms (cache hit)
2056 http cache camelcase@https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz 0ms (cache hit)
2057 http cache strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 0ms (cache hit)
2058 http cache semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 0ms (cache hit)
2059 http cache ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz 0ms (cache hit)
2060 http cache ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 0ms (cache hit)
2061 silly tarball no local data for concat-map@https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz. Extracting by manifest.
2062 silly tarball no local data for wrappy@https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz. Extracting by manifest.
2063 silly tarball no local data for once@https://registry.npmjs.org/once/-/once-1.4.0.tgz. Extracting by manifest.
2064 silly tarball no local data for inherits@https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz. Extracting by manifest.
2065 silly tarball no local data for inflight@https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz. Extracting by manifest.
2066 silly tarball no local data for fs.realpath@https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz. Extracting by manifest.
2067 silly tarball no local data for p-try@https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz. Extracting by manifest.
2068 silly tarball no local data for path-is-absolute@https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz. Extracting by manifest.
2069 silly tarball no local data for yargs-parser@https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz. Extracting by manifest.
2070 silly tarball no local data for y18n@https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz. Extracting by manifest.
2071 silly tarball no local data for require-directory@https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz. Extracting by manifest.
2072 silly tarball no local data for imurmurhash@https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz. Extracting by manifest.
2073 silly tarball no local data for get-caller-file@https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz. Extracting by manifest.
2074 silly tarball no local data for tmpl@https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz. Extracting by manifest.
2075 silly tarball no local data for cliui@https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz. Extracting by manifest.
2076 silly tarball no local data for isexe@https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz. Extracting by manifest.
2077 silly tarball no local data for escalade@https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz. Extracting by manifest.
2078 silly tarball no local data for @tybys/wasm-util@https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.10.1.tgz. Extracting by manifest.
2079 silly tarball no local data for tslib@https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz. Extracting by manifest.
2080 silly tarball no local data for @emnapi/wasi-threads@https://registry.npmjs.org/@emnapi/wasi-threads/-/wasi-threads-1.1.0.tgz. Extracting by manifest.
2081 silly tarball no local data for @emnapi/runtime@https://registry.npmjs.org/@emnapi/runtime/-/runtime-1.5.0.tgz. Extracting by manifest.
2082 silly tarball no local data for makeerror@https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz. Extracting by manifest.
2083 silly tarball no local data for napi-postinstall@https://registry.npmjs.org/napi-postinstall/-/napi-postinstall-0.3.3.tgz. Extracting by manifest.
2084 silly tarball no local data for @emnapi/core@https://registry.npmjs.org/@emnapi/core/-/core-1.5.0.tgz. Extracting by manifest.
2085 silly tarball no local data for is-number@https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz. Extracting by manifest.
2086 silly tarball no local data for @pkgr/core@https://registry.npmjs.org/@pkgr/core/-/core-0.2.9.tgz. Extracting by manifest.
2087 silly tarball no local data for has-flag@https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz. Extracting by manifest.
2088 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.2.2.tgz. Extracting by manifest.
2089 silly tarball no local data for is-fullwidth-code-point@https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz. Extracting by manifest.
2090 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz. Extracting by manifest.
2091 silly tarball no local data for eastasianwidth@https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz. Extracting by manifest.
2092 silly tarball no local data for @napi-rs/wasm-runtime@https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.12.tgz. Extracting by manifest.
2093 silly tarball no local data for char-regex@https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz. Extracting by manifest.
2094 silly tarball no local data for @unrs/resolver-binding-darwin-arm64@https://registry.npmjs.org/@unrs/resolver-binding-darwin-arm64/-/resolver-binding-darwin-arm64-1.11.1.tgz. Extracting by manifest.
2095 silly tarball no local data for source-map@https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz. Extracting by manifest.
2096 silly tarball no local data for escape-string-regexp@https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz. Extracting by manifest.
2097 silly tarball no local data for shebang-regex@https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz. Extracting by manifest.
2098 silly tarball no local data for react-is@https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz. Extracting by manifest.
2099 silly tarball no local data for buffer-from@https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz. Extracting by manifest.
2100 silly tarball no local data for is-arrayish@https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz. Extracting by manifest.
2101 silly tarball no local data for lines-and-columns@https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz. Extracting by manifest.
2102 silly tarball no local data for json-parse-even-better-errors@https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz. Extracting by manifest.
2103 silly tarball no local data for mimic-fn@https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz. Extracting by manifest.
2104 silly tarball no local data for balanced-match@https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz. Extracting by manifest.
2105 silly tarball no local data for brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz. Extracting by manifest.
2106 silly tarball no local data for fill-range@https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz. Extracting by manifest.
2107 silly tarball no local data for braces@https://registry.npmjs.org/braces/-/braces-3.0.3.tgz. Extracting by manifest.
2108 silly tarball no local data for yallist@https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz. Extracting by manifest.
2109 silly tarball no local data for error-ex@https://registry.npmjs.org/error-ex/-/error-ex-1.3.4.tgz. Extracting by manifest.
2110 silly tarball no local data for p-locate@https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz. Extracting by manifest.
2111 silly tarball no local data for sprintf-js@https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz. Extracting by manifest.
2112 silly tarball no local data for esprima@https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz. Extracting by manifest.
2113 silly tarball no local data for to-regex-range@https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz. Extracting by manifest.
2114 silly tarball no local data for argparse@https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz. Extracting by manifest.
2115 silly tarball no local data for @ungap/structured-clone@https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz. Extracting by manifest.
2116 silly tarball no local data for yocto-queue@https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz. Extracting by manifest.
2117 silly tarball no local data for synckit@https://registry.npmjs.org/synckit/-/synckit-0.11.11.tgz. Extracting by manifest.
2118 silly tarball no local data for leven@https://registry.npmjs.org/leven/-/leven-3.1.0.tgz. Extracting by manifest.
2119 silly tarball no local data for @jest/snapshot-utils@https://registry.npmjs.org/@jest/snapshot-utils/-/snapshot-utils-30.2.0.tgz. Extracting by manifest.
2120 silly tarball no local data for @babel/plugin-syntax-jsx@https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz. Extracting by manifest.
2121 silly tarball no local data for @babel/plugin-syntax-typescript@https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz. Extracting by manifest.
2122 silly tarball no local data for strip-bom@https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz. Extracting by manifest.
2123 silly tarball no local data for natural-compare@https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz. Extracting by manifest.
2124 silly tarball no local data for @jest/source-map@https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.1.tgz. Extracting by manifest.
2125 silly tarball no local data for @jest/globals@https://registry.npmjs.org/@jest/globals/-/globals-30.2.0.tgz. Extracting by manifest.
2126 silly tarball no local data for source-map-support@https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz. Extracting by manifest.
2127 silly tarball no local data for jest-leak-detector@https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.2.0.tgz. Extracting by manifest.
2128 silly tarball no local data for unrs-resolver@https://registry.npmjs.org/unrs-resolver/-/unrs-resolver-1.11.1.tgz. Extracting by manifest.
2129 silly tarball no local data for jest-pnp-resolver@https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz. Extracting by manifest.
2130 silly tarball no local data for callsites@https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz. Extracting by manifest.
2131 silly tarball no local data for @types/stack-utils@https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz. Extracting by manifest.
2132 silly tarball no local data for @jest/diff-sequences@https://registry.npmjs.org/@jest/diff-sequences/-/diff-sequences-30.0.1.tgz. Extracting by manifest.
2133 silly tarball no local data for jest-diff@https://registry.npmjs.org/jest-diff/-/jest-diff-30.2.0.tgz. Extracting by manifest.
2134 silly tarball no local data for emittery@https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz. Extracting by manifest.
2135 silly tarball no local data for bser@https://registry.npmjs.org/bser/-/bser-2.1.1.tgz. Extracting by manifest.
2136 silly tarball no local data for cjs-module-lexer@https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-2.1.0.tgz. Extracting by manifest.
2137 silly tarball no local data for node-int64@https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz. Extracting by manifest.
2138 silly tarball no local data for walker@https://registry.npmjs.org/walker/-/walker-1.0.8.tgz. Extracting by manifest.
2139 silly tarball no local data for picomatch@https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz. Extracting by manifest.
2140 silly tarball no local data for normalize-path@https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz. Extracting by manifest.
2141 silly tarball no local data for fb-watchman@https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz. Extracting by manifest.
2142 silly tarball no local data for anymatch@https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz. Extracting by manifest.
2143 silly tarball no local data for detect-newline@https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz. Extracting by manifest.
2144 silly tarball no local data for fsevents@https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz. Extracting by manifest.
2145 silly tarball no local data for @jest/expect-utils@https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.2.0.tgz. Extracting by manifest.
2146 silly tarball no local data for @sinonjs/commons@https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz. Extracting by manifest.
2147 silly tarball no local data for @sinonjs/fake-timers@https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-13.0.5.tgz. Extracting by manifest.
2148 silly tarball no local data for expect@https://registry.npmjs.org/expect/-/expect-30.2.0.tgz. Extracting by manifest.
2149 silly tarball no local data for jest-mock@https://registry.npmjs.org/jest-mock/-/jest-mock-30.2.0.tgz. Extracting by manifest.
2150 silly tarball no local data for @jest/fake-timers@https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.2.0.tgz. Extracting by manifest.
2151 silly tarball no local data for stack-utils@https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz. Extracting by manifest.
2152 silly tarball no local data for pure-rand@https://registry.npmjs.org/pure-rand/-/pure-rand-7.0.1.tgz. Extracting by manifest.
2153 silly tarball no local data for jest-matcher-utils@https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-30.2.0.tgz. Extracting by manifest.
2154 silly tarball no local data for jest-each@https://registry.npmjs.org/jest-each/-/jest-each-30.2.0.tgz. Extracting by manifest.
2155 silly tarball no local data for is-generator-fn@https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz. Extracting by manifest.
2156 silly tarball no local data for dedent@https://registry.npmjs.org/dedent/-/dedent-1.7.0.tgz. Extracting by manifest.
2157 silly tarball no local data for co@https://registry.npmjs.org/co/-/co-4.6.0.tgz. Extracting by manifest.
2158 silly tarball no local data for @jest/expect@https://registry.npmjs.org/@jest/expect/-/expect-30.2.0.tgz. Extracting by manifest.
2159 silly tarball no local data for @jest/environment@https://registry.npmjs.org/@jest/environment/-/environment-30.2.0.tgz. Extracting by manifest.
2160 silly tarball no local data for @babel/plugin-syntax-top-level-await@https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz. Extracting by manifest.
2161 silly tarball no local data for @babel/plugin-syntax-private-property-in-object@https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz. Extracting by manifest.
2162 silly tarball no local data for @babel/plugin-syntax-optional-chaining@https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz. Extracting by manifest.
2163 silly tarball no local data for @babel/plugin-syntax-optional-catch-binding@https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz. Extracting by manifest.
2164 silly tarball no local data for @babel/plugin-syntax-object-rest-spread@https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz. Extracting by manifest.
2165 silly tarball no local data for @babel/plugin-syntax-numeric-separator@https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz. Extracting by manifest.
2166 silly tarball no local data for @babel/plugin-syntax-nullish-coalescing-operator@https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz. Extracting by manifest.
2167 silly tarball no local data for @babel/plugin-syntax-logical-assignment-operators@https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz. Extracting by manifest.
2168 silly tarball no local data for @babel/plugin-syntax-json-strings@https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz. Extracting by manifest.
2169 silly tarball no local data for @babel/plugin-syntax-import-meta@https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz. Extracting by manifest.
2170 silly tarball no local data for @babel/plugin-syntax-import-attributes@https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz. Extracting by manifest.
2171 silly tarball no local data for @babel/plugin-syntax-class-static-block@https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz. Extracting by manifest.
2172 silly tarball no local data for @babel/plugin-syntax-class-properties@https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz. Extracting by manifest.
2173 silly tarball no local data for @babel/plugin-syntax-bigint@https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz. Extracting by manifest.
2174 silly tarball no local data for @babel/plugin-syntax-async-generators@https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz. Extracting by manifest.
2175 silly tarball no local data for babel-preset-current-node-syntax@https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.2.0.tgz. Extracting by manifest.
2176 silly tarball no local data for babel-plugin-jest-hoist@https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.2.0.tgz. Extracting by manifest.
2177 silly tarball no local data for @types/babel__traverse@https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.28.0.tgz. Extracting by manifest.
2178 silly tarball no local data for @types/babel__template@https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz. Extracting by manifest.
2179 silly tarball no local data for @types/babel__generator@https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz. Extracting by manifest.
2180 silly tarball no local data for babel-preset-jest@https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.2.0.tgz. Extracting by manifest.
2181 silly tarball no local data for @types/babel__core@https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz. Extracting by manifest.
2182 silly tarball no local data for strip-json-comments@https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz. Extracting by manifest.
2183 silly tarball no local data for parse-json@https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz. Extracting by manifest.
2184 silly tarball no local data for jest-environment-node@https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-30.2.0.tgz. Extracting by manifest.
2185 silly tarball no local data for jest-docblock@https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.2.0.tgz. Extracting by manifest.
2186 silly tarball no local data for jest-circus@https://registry.npmjs.org/jest-circus/-/jest-circus-30.2.0.tgz. Extracting by manifest.
2187 silly tarball no local data for deepmerge@https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz. Extracting by manifest.
2188 silly tarball no local data for babel-jest@https://registry.npmjs.org/babel-jest/-/babel-jest-30.2.0.tgz. Extracting by manifest.
2189 silly tarball no local data for @jest/test-sequencer@https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.2.0.tgz. Extracting by manifest.
2190 silly tarball no local data for yargs@https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz. Extracting by manifest.
2191 silly tarball no local data for @jest/get-type@https://registry.npmjs.org/@jest/get-type/-/get-type-30.1.0.tgz. Extracting by manifest.
2192 silly tarball no local data for strip-final-newline@https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz. Extracting by manifest.
2193 silly tarball no local data for npm-run-path@https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz. Extracting by manifest.
2194 silly tarball no local data for onetime@https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz. Extracting by manifest.
2195 silly tarball no local data for merge-stream@https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz. Extracting by manifest.
2196 silly tarball no local data for is-stream@https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz. Extracting by manifest.
2197 silly tarball no local data for get-stream@https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz. Extracting by manifest.
2198 silly tarball no local data for human-signals@https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz. Extracting by manifest.
2199 silly tarball no local data for execa@https://registry.npmjs.org/execa/-/execa-5.1.1.tgz. Extracting by manifest.
2200 silly tarball no local data for p-limit@https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz. Extracting by manifest.
2201 silly tarball no local data for wrap-ansi-cjs@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz. Extracting by manifest.
2202 silly tarball no local data for wrap-ansi@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz. Extracting by manifest.
2203 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.2.tgz. Extracting by manifest.
2204 silly tarball no local data for strip-ansi-cjs@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
2205 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz. Extracting by manifest.
2206 silly tarball no local data for string-width-cjs@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
2207 silly tarball no local data for @pkgjs/parseargs@https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz. Extracting by manifest.
2208 silly tarball no local data for @isaacs/cliui@https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz. Extracting by manifest.
2209 silly tarball no local data for html-escaper@https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz. Extracting by manifest.
2210 silly tarball no local data for make-dir@https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz. Extracting by manifest.
2211 silly tarball no local data for pkg-dir@https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz. Extracting by manifest.
2212 silly tarball no local data for resolve-cwd@https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz. Extracting by manifest.
2213 silly tarball no local data for shebang-command@https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz. Extracting by manifest.
2214 silly tarball no local data for which@https://registry.npmjs.org/which/-/which-2.0.2.tgz. Extracting by manifest.
2215 silly tarball no local data for signal-exit@https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz. Extracting by manifest.
2216 silly tarball no local data for path-key@https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz. Extracting by manifest.
2217 silly tarball no local data for cross-spawn@https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz. Extracting by manifest.
2218 silly tarball no local data for path-scurry@https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz. Extracting by manifest.
2219 silly tarball no local data for package-json-from-dist@https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz. Extracting by manifest.
2220 silly tarball no local data for minipass@https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz. Extracting by manifest.
2221 silly tarball no local data for jackspeak@https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz. Extracting by manifest.
2222 silly tarball no local data for minimatch@https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz. Extracting by manifest.
2223 silly tarball no local data for foreground-child@https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz. Extracting by manifest.
2224 silly tarball no local data for locate-path@https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz. Extracting by manifest.
2225 silly tarball no local data for path-exists@https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz. Extracting by manifest.
2226 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.1.3.tgz. Extracting by manifest.
2227 silly tarball no local data for color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz. Extracting by manifest.
2228 silly tarball no local data for color-convert@https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz. Extracting by manifest.
2229 silly tarball no local data for supports-color@https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz. Extracting by manifest.
2230 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz. Extracting by manifest.
2231 silly tarball no local data for node-releases@https://registry.npmjs.org/node-releases/-/node-releases-2.0.21.tgz. Extracting by manifest.
2232 silly tarball no local data for update-browserslist-db@https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz. Extracting by manifest.
2233 silly tarball no local data for electron-to-chromium@https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.227.tgz. Extracting by manifest.
2234 silly tarball no local data for caniuse-lite@https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001745.tgz. Extracting by manifest.
2235 silly tarball no local data for baseline-browser-mapping@https://registry.npmjs.org/baseline-browser-mapping/-/baseline-browser-mapping-2.8.8.tgz. Extracting by manifest.
2236 silly tarball no local data for resolve-from@https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz. Extracting by manifest.
2237 silly tarball no local data for js-yaml@https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz. Extracting by manifest.
2238 silly tarball no local data for get-package-type@https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz. Extracting by manifest.
2239 silly tarball no local data for find-up@https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz. Extracting by manifest.
2240 silly tarball no local data for camelcase@https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz. Extracting by manifest.
2241 silly tarball no local data for @istanbuljs/schema@https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz. Extracting by manifest.
2242 silly tarball no local data for test-exclude@https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz. Extracting by manifest.
2243 silly tarball no local data for @istanbuljs/load-nyc-config@https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz. Extracting by manifest.
2244 silly tarball no local data for @babel/helper-plugin-utils@https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz. Extracting by manifest.
2245 silly tarball no local data for type-fest@https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz. Extracting by manifest.
2246 silly tarball no local data for @types/yargs-parser@https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz. Extracting by manifest.
2247 silly tarball no local data for undici-types@https://registry.npmjs.org/undici-types/-/undici-types-7.12.0.tgz. Extracting by manifest.
2248 silly tarball no local data for @types/istanbul-lib-report@https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz. Extracting by manifest.
2249 silly tarball no local data for @jridgewell/sourcemap-codec@https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.5.tgz. Extracting by manifest.
2250 silly tarball no local data for @jridgewell/resolve-uri@https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz. Extracting by manifest.
2251 silly tarball no local data for @sinclair/typebox@https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.34.41.tgz. Extracting by manifest.
2252 silly tarball no local data for @types/yargs@https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz. Extracting by manifest.
2253 silly tarball no local data for @jest/schemas@https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.5.tgz. Extracting by manifest.
2254 silly tarball no local data for @types/istanbul-reports@https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz. Extracting by manifest.
2255 silly tarball no local data for @babel/helper-string-parser@https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz. Extracting by manifest.
2256 silly tarball no local data for @babel/helper-globals@https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz. Extracting by manifest.
2257 silly tarball no local data for lru-cache@https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz. Extracting by manifest.
2258 silly tarball no local data for @babel/helper-module-imports@https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz. Extracting by manifest.
2259 silly tarball no local data for browserslist@https://registry.npmjs.org/browserslist/-/browserslist-4.26.2.tgz. Extracting by manifest.
2260 silly tarball no local data for @babel/compat-data@https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.4.tgz. Extracting by manifest.
2261 silly tarball no local data for jsesc@https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz. Extracting by manifest.
2262 silly tarball no local data for @babel/helper-validator-option@https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz. Extracting by manifest.
2263 silly tarball no local data for picocolors@https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz. Extracting by manifest.
2264 silly tarball no local data for @jridgewell/gen-mapping@https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.13.tgz. Extracting by manifest.
2265 silly tarball no local data for js-tokens@https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz. Extracting by manifest.
2266 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-6.3.1.tgz. Extracting by manifest.
2267 silly tarball no local data for @babel/helper-validator-identifier@https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz. Extracting by manifest.
2268 silly tarball no local data for json5@https://registry.npmjs.org/json5/-/json5-2.2.3.tgz. Extracting by manifest.
2269 silly tarball no local data for gensync@https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz. Extracting by manifest.
2270 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-4.4.3.tgz. Extracting by manifest.
2271 silly tarball no local data for @babel/types@https://registry.npmjs.org/@babel/types/-/types-7.28.4.tgz. Extracting by manifest.
2272 silly tarball no local data for @jridgewell/remapping@https://registry.npmjs.org/@jridgewell/remapping/-/remapping-2.3.5.tgz. Extracting by manifest.
2273 silly tarball no local data for @babel/traverse@https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.4.tgz. Extracting by manifest.
2274 silly tarball no local data for @babel/template@https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz. Extracting by manifest.
2275 silly tarball no local data for @babel/helpers@https://registry.npmjs.org/@babel/helpers/-/helpers-7.28.4.tgz. Extracting by manifest.
2276 silly tarball no local data for @babel/parser@https://registry.npmjs.org/@babel/parser/-/parser-7.28.4.tgz. Extracting by manifest.
2277 silly tarball no local data for @babel/helper-module-transforms@https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.28.3.tgz. Extracting by manifest.
2278 silly tarball no local data for @babel/helper-compilation-targets@https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz. Extracting by manifest.
2279 silly tarball no local data for @babel/generator@https://registry.npmjs.org/@babel/generator/-/generator-7.28.3.tgz. Extracting by manifest.
2280 silly tarball no local data for write-file-atomic@https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-5.0.1.tgz. Extracting by manifest.
2281 silly tarball no local data for pirates@https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz. Extracting by manifest.
2282 silly tarball no local data for fast-json-stable-stringify@https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz. Extracting by manifest.
2283 silly tarball no local data for @babel/core@https://registry.npmjs.org/@babel/core/-/core-7.28.4.tgz. Extracting by manifest.
2284 silly tarball no local data for babel-plugin-istanbul@https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-7.0.1.tgz. Extracting by manifest.
2285 silly tarball no local data for v8-to-istanbul@https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz. Extracting by manifest.
2286 silly tarball no local data for @types/istanbul-lib-coverage@https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz. Extracting by manifest.
2287 silly tarball no local data for string-length@https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz. Extracting by manifest.
2288 silly tarball no local data for jest-worker@https://registry.npmjs.org/jest-worker/-/jest-worker-30.2.0.tgz. Extracting by manifest.
2289 silly tarball no local data for istanbul-reports@https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.2.0.tgz. Extracting by manifest.
2290 silly tarball no local data for istanbul-lib-source-maps@https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-5.0.6.tgz. Extracting by manifest.
2291 silly tarball no local data for istanbul-lib-report@https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz. Extracting by manifest.
2292 silly tarball no local data for @babel/code-frame@https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz. Extracting by manifest.
2293 silly tarball no local data for convert-source-map@https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz. Extracting by manifest.
2294 silly tarball no local data for istanbul-lib-coverage@https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz. Extracting by manifest.
2295 silly tarball no local data for istanbul-lib-instrument@https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz. Extracting by manifest.
2296 silly tarball no local data for glob@https://registry.npmjs.org/glob/-/glob-10.4.5.tgz. Extracting by manifest.
2297 silly tarball no local data for collect-v8-coverage@https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz. Extracting by manifest.
2298 silly tarball no local data for @jridgewell/trace-mapping@https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.31.tgz. Extracting by manifest.
2299 silly tarball no local data for @bcoe/v8-coverage@https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz. Extracting by manifest.
2300 silly tarball no local data for slash@https://registry.npmjs.org/slash/-/slash-3.0.0.tgz. Extracting by manifest.
2301 silly tarball no local data for pretty-format@https://registry.npmjs.org/pretty-format/-/pretty-format-30.2.0.tgz. Extracting by manifest.
2302 silly tarball no local data for micromatch@https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz. Extracting by manifest.
2303 silly tarball no local data for jest-watcher@https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.2.0.tgz. Extracting by manifest.
2304 silly tarball no local data for jest-validate@https://registry.npmjs.org/jest-validate/-/jest-validate-30.2.0.tgz. Extracting by manifest.
2305 silly tarball no local data for jest-util@https://registry.npmjs.org/jest-util/-/jest-util-30.2.0.tgz. Extracting by manifest.
2306 silly tarball no local data for jest-snapshot@https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-30.2.0.tgz. Extracting by manifest.
2307 silly tarball no local data for jest-runtime@https://registry.npmjs.org/jest-runtime/-/jest-runtime-30.2.0.tgz. Extracting by manifest.
2308 silly tarball no local data for jest-resolve-dependencies@https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-30.2.0.tgz. Extracting by manifest.
2309 silly tarball no local data for jest-resolve@https://registry.npmjs.org/jest-resolve/-/jest-resolve-30.2.0.tgz. Extracting by manifest.
2310 silly tarball no local data for jest-runner@https://registry.npmjs.org/jest-runner/-/jest-runner-30.2.0.tgz. Extracting by manifest.
2311 silly tarball no local data for jest-haste-map@https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-30.2.0.tgz. Extracting by manifest.
2312 silly tarball no local data for jest-message-util@https://registry.npmjs.org/jest-message-util/-/jest-message-util-30.2.0.tgz. Extracting by manifest.
2313 silly tarball no local data for jest-regex-util@https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.1.tgz. Extracting by manifest.
2314 silly tarball no local data for jest-changed-files@https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.2.0.tgz. Extracting by manifest.
2315 silly tarball no local data for graceful-fs@https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz. Extracting by manifest.
2316 silly tarball no local data for jest-config@https://registry.npmjs.org/jest-config/-/jest-config-30.2.0.tgz. Extracting by manifest.
2317 silly tarball no local data for ci-info@https://registry.npmjs.org/ci-info/-/ci-info-4.3.0.tgz. Extracting by manifest.
2318 silly tarball no local data for ansi-escapes@https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz. Extracting by manifest.
2319 silly tarball no local data for chalk@https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz. Extracting by manifest.
2320 silly tarball no local data for exit-x@https://registry.npmjs.org/exit-x/-/exit-x-0.2.2.tgz. Extracting by manifest.
2321 silly tarball no local data for @types/node@https://registry.npmjs.org/@types/node/-/node-24.5.2.tgz. Extracting by manifest.
2322 silly tarball no local data for @jest/console@https://registry.npmjs.org/@jest/console/-/console-30.2.0.tgz. Extracting by manifest.
2323 silly tarball no local data for @jest/reporters@https://registry.npmjs.org/@jest/reporters/-/reporters-30.2.0.tgz. Extracting by manifest.
2324 silly tarball no local data for type-detect@https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz. Extracting by manifest.
2325 silly tarball no local data for jest-cli@https://registry.npmjs.org/jest-cli/-/jest-cli-30.2.0.tgz. Extracting by manifest.
2326 silly tarball no local data for @jest/pattern@https://registry.npmjs.org/@jest/pattern/-/pattern-30.0.1.tgz. Extracting by manifest.
2327 silly tarball no local data for @jest/core@https://registry.npmjs.org/@jest/core/-/core-30.2.0.tgz. Extracting by manifest.
2328 silly tarball no local data for @jest/types@https://registry.npmjs.org/@jest/types/-/types-30.2.0.tgz. Extracting by manifest.
2329 silly tarball no local data for import-local@https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz. Extracting by manifest.
2330 silly tarball no local data for jest@https://registry.npmjs.org/jest/-/jest-30.2.0.tgz. Extracting by manifest.
2331 silly tarball no local data for @jest/transform@https://registry.npmjs.org/@jest/transform/-/transform-30.2.0.tgz. Extracting by manifest.
2332 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
2333 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz. Extracting by manifest.
2334 silly tarball no local data for wrap-ansi@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz. Extracting by manifest.
2335 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
2336 silly tarball no local data for @jest/test-result@https://registry.npmjs.org/@jest/test-result/-/test-result-30.2.0.tgz. Extracting by manifest.
2337 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
2338 silly tarball no local data for p-limit@https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz. Extracting by manifest.
2339 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
2340 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
2341 silly tarball no local data for signal-exit@https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz. Extracting by manifest.
2342 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz. Extracting by manifest.
2343 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
2344 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
2345 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.3.tgz. Extracting by manifest.
2346 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz. Extracting by manifest.
2347 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz. Extracting by manifest.
2348 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
2349 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
2350 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
2351 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
2352 silly tarball no local data for lru-cache@https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz. Extracting by manifest.
2353 silly tarball no local data for minimatch@https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz. Extracting by manifest.
2354 silly tarball no local data for brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz. Extracting by manifest.
2355 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz. Extracting by manifest.
2356 silly tarball no local data for glob@https://registry.npmjs.org/glob/-/glob-7.2.3.tgz. Extracting by manifest.
2357 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
2358 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
2359 silly tarball no local data for supports-color@https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz. Extracting by manifest.
2360 silly tarball no local data for picomatch@https://registry.npmjs.org/picomatch/-/picomatch-4.0.3.tgz. Extracting by manifest.
2361 silly tarball no local data for camelcase@https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz. Extracting by manifest.
2362 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
2363 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
2364 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz. Extracting by manifest.
2365 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz. Extracting by manifest.
2366 http fetch GET 200 https://registry.npmjs.org/once/-/once-1.4.0.tgz 125ms (cache miss)
2367 http fetch GET 200 https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz 127ms (cache miss)
2368 http fetch GET 200 https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz 125ms (cache miss)
2369 http fetch GET 200 https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz 125ms (cache miss)
2370 warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
2371 http fetch GET 200 https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz 126ms (cache miss)
2372 http fetch GET 200 https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz 127ms (cache miss)
2373 http fetch GET 200 https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz 162ms (cache miss)
2374 http fetch GET 200 https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz 163ms (cache miss)
2375 http fetch GET 200 https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz 163ms (cache miss)
2376 http fetch GET 200 https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz 163ms (cache miss)
2377 http fetch GET 200 https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz 177ms (cache miss)
2378 http fetch GET 200 https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz 183ms (cache miss)
2379 http fetch GET 200 https://registry.npmjs.org/napi-postinstall/-/napi-postinstall-0.3.3.tgz 193ms (cache miss)
2380 http fetch GET 200 https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz 195ms (cache miss)
2381 http fetch GET 200 https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz 197ms (cache miss)
2382 http fetch GET 200 https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.10.1.tgz 199ms (cache miss)
2383 http fetch GET 200 https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz 202ms (cache miss)
2384 http fetch GET 200 https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz 203ms (cache miss)
2385 http fetch GET 200 https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz 205ms (cache miss)
2386 http fetch GET 200 https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz 205ms (cache miss)
2387 http fetch GET 200 https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz 206ms (cache miss)
2388 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 254ms
2389 silly audit report {}
2390 http fetch GET 200 https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz 218ms (cache miss)
2391 http fetch GET 200 https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz 218ms (cache miss)
2392 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.2.2.tgz 220ms (cache miss)
2393 http fetch GET 200 https://registry.npmjs.org/@emnapi/runtime/-/runtime-1.5.0.tgz 225ms (cache miss)
2394 http fetch GET 200 https://registry.npmjs.org/@pkgr/core/-/core-0.2.9.tgz 227ms (cache miss)
2395 http fetch GET 200 https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz 226ms (cache miss)
2396 http fetch GET 200 https://registry.npmjs.org/@emnapi/wasi-threads/-/wasi-threads-1.1.0.tgz 229ms (cache miss)
2397 http fetch GET 200 https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz 229ms (cache miss)
2398 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz 230ms (cache miss)
2399 http fetch GET 200 https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz 231ms (cache miss)
2400 http fetch GET 200 https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz 229ms (cache miss)
2401 http fetch GET 200 https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz 240ms (cache miss)
2402 http fetch GET 200 https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz 241ms (cache miss)
2403 http fetch GET 200 https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz 241ms (cache miss)
2404 http fetch GET 200 https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz 244ms (cache miss)
2405 http fetch GET 200 https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz 247ms (cache miss)
2406 http fetch GET 200 https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz 251ms (cache miss)
2407 http fetch GET 200 https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz 253ms (cache miss)
2408 http fetch GET 200 https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz 255ms (cache miss)
2409 http fetch GET 200 https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz 260ms (cache miss)
2410 http fetch GET 200 https://registry.npmjs.org/braces/-/braces-3.0.3.tgz 263ms (cache miss)
2411 http fetch GET 200 https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz 266ms (cache miss)
2412 http fetch GET 200 https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz 277ms (cache miss)
2413 http fetch GET 200 https://registry.npmjs.org/@emnapi/core/-/core-1.5.0.tgz 293ms (cache miss)
2414 http fetch GET 200 https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz 285ms (cache miss)
2415 http fetch GET 200 https://registry.npmjs.org/error-ex/-/error-ex-1.3.4.tgz 290ms (cache miss)
2416 http fetch GET 200 https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz 289ms (cache miss)
2417 http fetch GET 200 https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz 292ms (cache miss)
2418 http fetch GET 200 https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz 299ms (cache miss)
2419 http fetch GET 200 https://registry.npmjs.org/leven/-/leven-3.1.0.tgz 300ms (cache miss)
2420 http fetch GET 200 https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz 301ms (cache miss)
2421 http fetch GET 200 https://registry.npmjs.org/synckit/-/synckit-0.11.11.tgz 316ms (cache miss)
2422 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz 317ms (cache miss)
2423 http fetch GET 200 https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz 319ms (cache miss)
2424 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz 320ms (cache miss)
2425 http fetch GET 200 https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz 329ms (cache miss)
2426 http fetch GET 200 https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz 332ms (cache miss)
2427 http fetch GET 200 https://registry.npmjs.org/@jest/source-map/-/source-map-30.0.1.tgz 333ms (cache miss)
2428 http fetch GET 200 https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz 343ms (cache miss)
2429 http fetch GET 200 https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.2.0.tgz 333ms (cache miss)
2430 http fetch GET 200 https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz 335ms (cache miss)
2431 http fetch GET 200 https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz 334ms (cache miss)
2432 http fetch GET 200 https://registry.npmjs.org/@jest/globals/-/globals-30.2.0.tgz 336ms (cache miss)
2433 http fetch GET 200 https://registry.npmjs.org/@jest/snapshot-utils/-/snapshot-utils-30.2.0.tgz 338ms (cache miss)
2434 http fetch GET 200 https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz 339ms (cache miss)
2435 http fetch GET 200 https://registry.npmjs.org/unrs-resolver/-/unrs-resolver-1.11.1.tgz 338ms (cache miss)
2436 http fetch GET 200 https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz 354ms (cache miss)
2437 http fetch GET 200 https://registry.npmjs.org/jest-diff/-/jest-diff-30.2.0.tgz 358ms (cache miss)
2438 http fetch GET 200 https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz 367ms (cache miss)
2439 http fetch GET 200 https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz 372ms (cache miss)
2440 http fetch GET 200 https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz 372ms (cache miss)
2441 http fetch GET 200 https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz 374ms (cache miss)
2442 http fetch GET 200 https://registry.npmjs.org/bser/-/bser-2.1.1.tgz 374ms (cache miss)
2443 http fetch GET 200 https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz 374ms (cache miss)
2444 http fetch GET 200 https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz 375ms (cache miss)
2445 http fetch GET 200 https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz 375ms (cache miss)
2446 http fetch GET 200 https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-2.1.0.tgz 377ms (cache miss)
2447 http fetch GET 200 https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz 376ms (cache miss)
2448 http fetch GET 200 https://registry.npmjs.org/@jest/diff-sequences/-/diff-sequences-30.0.1.tgz 378ms (cache miss)
2449 http fetch GET 200 https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz 379ms (cache miss)
2450 http fetch GET 200 https://registry.npmjs.org/walker/-/walker-1.0.8.tgz 379ms (cache miss)
2451 http fetch GET 200 https://registry.npmjs.org/@unrs/resolver-binding-darwin-arm64/-/resolver-binding-darwin-arm64-1.11.1.tgz 393ms (cache miss)
2452 http fetch GET 200 https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.2.0.tgz 392ms (cache miss)
2453 http fetch GET 200 https://registry.npmjs.org/expect/-/expect-30.2.0.tgz 393ms (cache miss)
2454 http fetch GET 200 https://registry.npmjs.org/jest-each/-/jest-each-30.2.0.tgz 393ms (cache miss)
2455 http fetch GET 200 https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-30.2.0.tgz 421ms (cache miss)
2456 http fetch GET 200 https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz 423ms (cache miss)
2457 http fetch GET 200 https://registry.npmjs.org/jest-mock/-/jest-mock-30.2.0.tgz 422ms (cache miss)
2458 http fetch GET 200 https://registry.npmjs.org/dedent/-/dedent-1.7.0.tgz 422ms (cache miss)
2459 http fetch GET 200 https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz 424ms (cache miss)
2460 http fetch GET 200 https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-30.2.0.tgz 424ms (cache miss)
2461 http fetch GET 200 https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz 426ms (cache miss)
2462 http fetch GET 200 https://registry.npmjs.org/@jest/environment/-/environment-30.2.0.tgz 433ms (cache miss)
2463 http fetch GET 200 https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-13.0.5.tgz 436ms (cache miss)
2464 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz 436ms (cache miss)
2465 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz 437ms (cache miss)
2466 http fetch GET 200 https://registry.npmjs.org/@jest/expect/-/expect-30.2.0.tgz 438ms (cache miss)
2467 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz 442ms (cache miss)
2468 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz 444ms (cache miss)
2469 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz 445ms (cache miss)
2470 http fetch GET 200 https://registry.npmjs.org/pure-rand/-/pure-rand-7.0.1.tgz 459ms (cache miss)
2471 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz 455ms (cache miss)
2472 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz 499ms (cache miss)
2473 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz 500ms (cache miss)
2474 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz 496ms (cache miss)
2475 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz 497ms (cache miss)
2476 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz 497ms (cache miss)
2477 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz 502ms (cache miss)
2478 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz 498ms (cache miss)
2479 http fetch GET 200 https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.2.0.tgz 498ms (cache miss)
2480 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz 502ms (cache miss)
2481 http fetch GET 200 https://registry.npmjs.org/co/-/co-4.6.0.tgz 507ms (cache miss)
2482 http fetch GET 200 https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz 500ms (cache miss)
2483 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz 505ms (cache miss)
2484 http fetch GET 200 https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.2.0.tgz 503ms (cache miss)
2485 http fetch GET 200 https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.28.0.tgz 505ms (cache miss)
2486 http fetch GET 200 https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-30.2.0.tgz 507ms (cache miss)
2487 http fetch GET 200 https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz 506ms (cache miss)
2488 http fetch GET 200 https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz 506ms (cache miss)
2489 http fetch GET 200 https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.2.0.tgz 507ms (cache miss)
2490 http fetch GET 200 https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-30.2.0.tgz 508ms (cache miss)
2491 http fetch GET 200 https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz 507ms (cache miss)
2492 http fetch GET 200 https://registry.npmjs.org/babel-jest/-/babel-jest-30.2.0.tgz 512ms (cache miss)
2493 http fetch GET 200 https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz 516ms (cache miss)
2494 http fetch GET 200 https://registry.npmjs.org/jest-circus/-/jest-circus-30.2.0.tgz 515ms (cache miss)
2495 http fetch GET 200 https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz 515ms (cache miss)
2496 http fetch GET 200 https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz 518ms (cache miss)
2497 http fetch GET 200 https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz 517ms (cache miss)
2498 http fetch GET 200 https://registry.npmjs.org/@jest/get-type/-/get-type-30.1.0.tgz 522ms (cache miss)
2499 http fetch GET 200 https://registry.npmjs.org/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.12.tgz 551ms (cache miss)
2500 http fetch GET 200 https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-30.2.0.tgz 528ms (cache miss)
2501 http fetch GET 200 https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz 526ms (cache miss)
2502 http fetch GET 200 https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz 536ms (cache miss)
2503 http fetch GET 200 https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz 533ms (cache miss)
2504 http fetch GET 200 https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz 541ms (cache miss)
2505 http fetch GET 200 https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz 541ms (cache miss)
2506 http fetch GET 200 https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz 551ms (cache miss)
2507 http fetch GET 200 https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz 554ms (cache miss)
2508 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.2.tgz 550ms (cache miss)
2509 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 549ms (cache miss)
2510 http fetch GET 200 https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz 560ms (cache miss)
2511 http fetch GET 200 https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz 573ms (cache miss)
2512 http fetch GET 200 https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz 573ms (cache miss)
2513 http fetch GET 200 https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz 573ms (cache miss)
2514 http fetch GET 200 https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz 574ms (cache miss)
2515 http fetch GET 200 https://registry.npmjs.org/which/-/which-2.0.2.tgz 577ms (cache miss)
2516 http fetch GET 200 https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz 593ms (cache miss)
2517 http fetch GET 200 https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz 614ms (cache miss)
2518 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz 625ms (cache miss)
2519 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 626ms (cache miss)
2520 http fetch GET 200 https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz 632ms (cache miss)
2521 http fetch GET 200 https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz 626ms (cache miss)
2522 http fetch GET 200 https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz 631ms (cache miss)
2523 http fetch GET 200 https://registry.npmjs.org/execa/-/execa-5.1.1.tgz 632ms (cache miss)
2524 http fetch GET 200 https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz 632ms (cache miss)
2525 http fetch GET 200 https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz 630ms (cache miss)
2526 http fetch GET 200 https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz 630ms (cache miss)
2527 http fetch GET 200 https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz 638ms (cache miss)
2528 http fetch GET 200 https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz 634ms (cache miss)
2529 http fetch GET 200 https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz 643ms (cache miss)
2530 http fetch GET 200 https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz 648ms (cache miss)
2531 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.1.3.tgz 650ms (cache miss)
2532 http fetch GET 200 https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz 651ms (cache miss)
2533 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz 650ms (cache miss)
2534 http fetch GET 200 https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz 653ms (cache miss)
2535 http fetch GET 200 https://registry.npmjs.org/node-releases/-/node-releases-2.0.21.tgz 653ms (cache miss)
2536 http fetch GET 200 https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz 657ms (cache miss)
2537 http fetch GET 200 https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz 661ms (cache miss)
2538 http fetch GET 200 https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz 671ms (cache miss)
2539 http fetch GET 200 https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz 670ms (cache miss)
2540 http fetch GET 200 https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz 668ms (cache miss)
2541 http fetch GET 200 https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz 668ms (cache miss)
2542 http fetch GET 200 https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz 666ms (cache miss)
2543 http fetch GET 200 https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz 668ms (cache miss)
2544 http fetch GET 200 https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.227.tgz 674ms (cache miss)
2545 http fetch GET 200 https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz 680ms (cache miss)
2546 http fetch GET 200 https://registry.npmjs.org/baseline-browser-mapping/-/baseline-browser-mapping-2.8.8.tgz 679ms (cache miss)
2547 http fetch GET 200 https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz 678ms (cache miss)
2548 http fetch GET 200 https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz 678ms (cache miss)
2549 http fetch GET 200 https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz 804ms (cache miss)
2550 http fetch GET 200 https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz 811ms (cache miss)
2551 http fetch GET 200 https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz 813ms (cache miss)
2552 http fetch GET 200 https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz 842ms (cache miss)
2553 http fetch GET 200 https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz 843ms (cache miss)
2554 http fetch GET 200 https://registry.npmjs.org/@jest/schemas/-/schemas-30.0.5.tgz 846ms (cache miss)
2555 http fetch GET 200 https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz 845ms (cache miss)
2556 http fetch GET 200 https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz 845ms (cache miss)
2557 http fetch GET 200 https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz 847ms (cache miss)
2558 http fetch GET 200 https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz 849ms (cache miss)
2559 http fetch GET 200 https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz 850ms (cache miss)
2560 http fetch GET 200 https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz 851ms (cache miss)
2561 http fetch GET 200 https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz 853ms (cache miss)
2562 http fetch GET 200 https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz 854ms (cache miss)
2563 http fetch GET 200 https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz 856ms (cache miss)
2564 http fetch GET 200 https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.4.tgz 856ms (cache miss)
2565 http fetch GET 200 https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz 866ms (cache miss)
2566 http fetch GET 200 https://registry.npmjs.org/browserslist/-/browserslist-4.26.2.tgz 873ms (cache miss)
2567 http fetch GET 200 https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz 878ms (cache miss)
2568 http fetch GET 200 https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz 883ms (cache miss)
2569 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-6.3.1.tgz 884ms (cache miss)
2570 http fetch GET 200 https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz 885ms (cache miss)
2571 http fetch GET 200 https://registry.npmjs.org/undici-types/-/undici-types-7.12.0.tgz 890ms (cache miss)
2572 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-4.4.3.tgz 886ms (cache miss)
2573 http fetch GET 200 https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz 893ms (cache miss)
2574 http fetch GET 200 https://registry.npmjs.org/@jridgewell/remapping/-/remapping-2.3.5.tgz 897ms (cache miss)
2575 http fetch GET 200 https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz 905ms (cache miss)
2576 http fetch GET 200 https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz 908ms (cache miss)
2577 http fetch GET 200 https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-5.0.1.tgz 920ms (cache miss)
2578 http fetch GET 200 https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-7.0.1.tgz 920ms (cache miss)
2579 http fetch GET 200 https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.13.tgz 970ms (cache miss)
2580 http fetch GET 200 https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz 973ms (cache miss)
2581 http fetch GET 200 https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz 973ms (cache miss)
2582 http fetch GET 200 https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz 978ms (cache miss)
2583 http fetch GET 200 https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz 985ms (cache miss)
2584 http fetch GET 200 https://registry.npmjs.org/json5/-/json5-2.2.3.tgz 984ms (cache miss)
2585 http fetch GET 200 https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz 982ms (cache miss)
2586 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-5.0.6.tgz 1001ms (cache miss)
2587 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz 1002ms (cache miss)
2588 http fetch GET 200 https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.28.3.tgz 1012ms (cache miss)
2589 http fetch GET 200 https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz 1010ms (cache miss)
2590 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz 1017ms (cache miss)
2591 http fetch GET 200 https://registry.npmjs.org/slash/-/slash-3.0.0.tgz 1019ms (cache miss)
2592 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz 1025ms (cache miss)
2593 http fetch GET 200 https://registry.npmjs.org/jest-validate/-/jest-validate-30.2.0.tgz 1024ms (cache miss)
2594 http fetch GET 200 https://registry.npmjs.org/pretty-format/-/pretty-format-30.2.0.tgz 1025ms (cache miss)
2595 http fetch GET 200 https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz 1025ms (cache miss)
2596 http fetch GET 200 https://registry.npmjs.org/jest-util/-/jest-util-30.2.0.tgz 1024ms (cache miss)
2597 http fetch GET 200 https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz 1029ms (cache miss)
2598 http fetch GET 200 https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-30.2.0.tgz 1028ms (cache miss)
2599 http fetch GET 200 https://registry.npmjs.org/jest-resolve/-/jest-resolve-30.2.0.tgz 1028ms (cache miss)
2600 http fetch GET 200 https://registry.npmjs.org/jest-runner/-/jest-runner-30.2.0.tgz 1028ms (cache miss)
2601 http fetch GET 200 https://registry.npmjs.org/jest-worker/-/jest-worker-30.2.0.tgz 1033ms (cache miss)
2602 http fetch GET 200 https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.2.0.tgz 1033ms (cache miss)
2603 http fetch GET 200 https://registry.npmjs.org/jest-runtime/-/jest-runtime-30.2.0.tgz 1033ms (cache miss)
2604 http fetch GET 200 https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz 1034ms (cache miss)
2605 http fetch GET 200 https://registry.npmjs.org/jest-message-util/-/jest-message-util-30.2.0.tgz 1037ms (cache miss)
2606 http fetch GET 200 https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-30.2.0.tgz 1070ms (cache miss)
2607 http fetch GET 200 https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-30.0.1.tgz 1084ms (cache miss)
2608 http fetch GET 200 https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.2.0.tgz 1084ms (cache miss)
2609 http fetch GET 200 https://registry.npmjs.org/exit-x/-/exit-x-0.2.2.tgz 1083ms (cache miss)
2610 http fetch GET 200 https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz 1085ms (cache miss)
2611 http fetch GET 200 https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz 1086ms (cache miss)
2612 http fetch GET 200 https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz 1086ms (cache miss)
2613 http fetch GET 200 https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz 1085ms (cache miss)
2614 http fetch GET 200 https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-30.2.0.tgz 1090ms (cache miss)
2615 http fetch GET 200 https://registry.npmjs.org/ci-info/-/ci-info-4.3.0.tgz 1090ms (cache miss)
2616 http fetch GET 200 https://registry.npmjs.org/jest-config/-/jest-config-30.2.0.tgz 1093ms (cache miss)
2617 http fetch GET 200 https://registry.npmjs.org/@jest/console/-/console-30.2.0.tgz 1101ms (cache miss)
2618 http fetch GET 200 https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz 1100ms (cache miss)
2619 http fetch GET 200 https://registry.npmjs.org/@jest/pattern/-/pattern-30.0.1.tgz 1101ms (cache miss)
2620 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 1100ms (cache miss)
2621 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 1099ms (cache miss)
2622 http fetch GET 200 https://registry.npmjs.org/jest-cli/-/jest-cli-30.2.0.tgz 1101ms (cache miss)
2623 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 1099ms (cache miss)
2624 http fetch GET 200 https://registry.npmjs.org/@jest/transform/-/transform-30.2.0.tgz 1100ms (cache miss)
2625 http fetch GET 200 https://registry.npmjs.org/@jest/types/-/types-30.2.0.tgz 1101ms (cache miss)
2626 http fetch GET 200 https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz 1100ms (cache miss)
2627 http fetch GET 200 https://registry.npmjs.org/@jest/reporters/-/reporters-30.2.0.tgz 1111ms (cache miss)
2628 http fetch GET 200 https://registry.npmjs.org/jest/-/jest-30.2.0.tgz 1110ms (cache miss)
2629 http fetch GET 200 https://registry.npmjs.org/@jest/core/-/core-30.2.0.tgz 1111ms (cache miss)
2630 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 1169ms (cache miss)
2631 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 1172ms (cache miss)
2632 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 1172ms (cache miss)
2633 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 1175ms (cache miss)
2634 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 1175ms (cache miss)
2635 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 1176ms (cache miss)
2636 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 1176ms (cache miss)
2637 http fetch GET 200 https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz 1177ms (cache miss)
2638 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 1180ms (cache miss)
2639 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.3.tgz 1184ms (cache miss)
2640 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 1187ms (cache miss)
2641 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 1185ms (cache miss)
2642 http fetch GET 200 https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz 1189ms (cache miss)
2643 http fetch GET 200 https://registry.npmjs.org/@jest/test-result/-/test-result-30.2.0.tgz 1190ms (cache miss)
2644 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 1190ms (cache miss)
2645 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 1189ms (cache miss)
2646 http fetch GET 200 https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz 1190ms (cache miss)
2647 http fetch GET 200 https://registry.npmjs.org/glob/-/glob-7.2.3.tgz 1191ms (cache miss)
2648 warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
2649 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 1191ms (cache miss)
2650 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 1191ms (cache miss)
2651 http fetch GET 200 https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz 1193ms (cache miss)
2652 http fetch GET 200 https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz 1195ms (cache miss)
2653 http fetch GET 200 https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.31.tgz 1213ms (cache miss)
2654 http fetch GET 200 https://registry.npmjs.org/picomatch/-/picomatch-4.0.3.tgz 1208ms (cache miss)
2655 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 1222ms (cache miss)
2656 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz 1227ms (cache miss)
2657 http fetch GET 200 https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz 1244ms (cache miss)
2658 http fetch GET 200 https://registry.npmjs.org/@babel/generator/-/generator-7.28.3.tgz 1261ms (cache miss)
2659 http fetch GET 200 https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz 1268ms (cache miss)
2660 http fetch GET 200 https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.5.tgz 1284ms (cache miss)
2661 http fetch GET 200 https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.2.0.tgz 1281ms (cache miss)
2662 http fetch GET 200 https://registry.npmjs.org/glob/-/glob-10.4.5.tgz 1280ms (cache miss)
2663 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 1272ms (cache miss)
2664 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 1271ms (cache miss)
2665 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 1279ms (cache miss)
2666 http fetch GET 200 https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.4.tgz 1296ms (cache miss)
2667 http fetch GET 200 https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz 1288ms (cache miss)
2668 http fetch GET 200 https://registry.npmjs.org/@babel/parser/-/parser-7.28.4.tgz 1303ms (cache miss)
2669 http fetch GET 200 https://registry.npmjs.org/@babel/core/-/core-7.28.4.tgz 1377ms (cache miss)
2670 http fetch GET 200 https://registry.npmjs.org/@babel/helpers/-/helpers-7.28.4.tgz 1458ms (cache miss)
2671 http fetch GET 200 https://registry.npmjs.org/@types/node/-/node-24.5.2.tgz 1451ms (cache miss)
2672 http fetch GET 200 https://registry.npmjs.org/@babel/types/-/types-7.28.4.tgz 1493ms (cache miss)
2673 http fetch GET 200 https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001745.tgz 1742ms (cache miss)
2674 http fetch GET 200 https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.34.41.tgz 1878ms (cache miss)
2675 info run fsevents@2.3.3 install node_modules/fsevents node-gyp rebuild
2676 info run fsevents@2.3.3 install { code: 1, signal: null }
2677 verbose reify failed optional dependency /Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/fsevents
2678 silly reify mark deleted [
2678 silly reify   '/Users/<USER>/tokeep/mvp/algonav-cloud-gui/.npm-cache/_npx/b8d86e6551a4f492/node_modules/fsevents'
2678 silly reify ]
2679 info run unrs-resolver@1.11.1 postinstall node_modules/unrs-resolver napi-postinstall unrs-resolver 1.11.1 check
2680 info run unrs-resolver@1.11.1 postinstall { code: 0, signal: null }
2681 verbose cwd /Users/<USER>/tokeep/mvp/algonav-cloud-gui
2682 verbose os Darwin 23.6.0
2683 verbose node v22.19.0
2684 verbose npm  v11.6.0
2685 notice
2685 notice New patch version of npm available! 11.6.0 -> 11.6.1
2685 notice Changelog: https://github.com/npm/cli/releases/tag/v11.6.1
2685 notice To update run: npm install -g npm@11.6.1
2685 notice  { force: true, [Symbol(proc-log.meta)]: true }
2686 verbose exit 1
2687 verbose code 1
